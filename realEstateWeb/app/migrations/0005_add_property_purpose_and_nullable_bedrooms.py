# Generated by Django 5.2.4 on 2025-07-18 16:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0004_add_land_area_and_maps'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='property_purpose',
            field=models.CharField(choices=[('land', 'Land (जग्गा)'), ('rent', 'Rent (भाडा)')], default='land', help_text='Property purpose - Land or Rent', max_length=10),
        ),
        migrations.AlterField(
            model_name='property',
            name='bedrooms',
            field=models.IntegerField(blank=True, help_text='Number of bedrooms (for rent properties)', null=True),
        ),
    ]
