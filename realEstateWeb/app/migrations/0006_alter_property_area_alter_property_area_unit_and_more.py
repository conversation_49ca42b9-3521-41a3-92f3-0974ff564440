# Generated by Django 5.2.4 on 2025-07-18 16:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0005_add_property_purpose_and_nullable_bedrooms'),
    ]

    operations = [
        migrations.AlterField(
            model_name='property',
            name='area',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Area in selected unit', max_digits=10, null=True),
        ),
        migrations.AlterField(
            model_name='property',
            name='area_unit',
            field=models.CharField(blank=True, choices=[('aana', 'Aana (आना)'), ('ropani', '<PERSON><PERSON><PERSON> (रोपनी)'), ('dhur', '<PERSON>hur (धुर)'), ('bigha', 'Bigha (बिघा)'), ('kattha', 'Kattha (कट्ठा)')], default='aana', help_text='Unit of area measurement', max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='property',
            name='price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True),
        ),
        migrations.AlterField(
            model_name='property',
            name='property_purpose',
            field=models.CharField(blank=True, choices=[('land', 'Land (जग्गा)'), ('rent', 'Rent (भाडा)')], default='land', help_text='Property purpose - Land or Rent', max_length=10, null=True),
        ),
    ]
