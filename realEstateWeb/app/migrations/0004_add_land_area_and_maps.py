# Generated by Django 5.2.4 on 2025-07-18 15:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0003_add_area_unit_to_property'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='google_maps_embed_url',
            field=models.URLField(blank=True, help_text='Google Maps embed URL', null=True),
        ),
        migrations.AddField(
            model_name='property',
            name='land_aana',
            field=models.IntegerField(blank=True, help_text='<PERSON>ana (आना)', null=True),
        ),
        migrations.AddField(
            model_name='property',
            name='land_daam',
            field=models.IntegerField(blank=True, help_text='Da<PERSON> (दाम)', null=True),
        ),
        migrations.AddField(
            model_name='property',
            name='land_paisa',
            field=models.IntegerField(blank=True, help_text='<PERSON><PERSON> (पैसा)', null=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='property',
            name='land_ropani',
            field=models.Integer<PERSON>ield(blank=True, help_text='<PERSON><PERSON><PERSON> (रोपनी)', null=True),
        ),
    ]
