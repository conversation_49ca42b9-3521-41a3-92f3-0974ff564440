# Generated by Django 5.2.4 on 2025-07-17 07:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0002_aboutus_agent_gallery_heroslide_journeystep_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='area_unit',
            field=models.CharField(choices=[('aana', 'A<PERSON> (आना)'), ('ropani', '<PERSON><PERSON><PERSON> (रोपनी)'), ('dhur', '<PERSON><PERSON> (धुर)'), ('bigha', 'Bigha (बिघा)'), ('kattha', '<PERSON>tha (कट्ठा)')], default='aana', help_text='Unit of area measurement', max_length=10),
        ),
        migrations.AlterField(
            model_name='property',
            name='area',
            field=models.DecimalField(decimal_places=2, help_text='Area in selected unit', max_digits=10),
        ),
    ]
