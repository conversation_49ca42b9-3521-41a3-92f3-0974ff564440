import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
// Removed react-toastify to avoid conflicts with shadcn/ui toast system
import { Layout } from "@/components/layout/Layout";
import { AuthProvider } from "@/contexts/AuthContext";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { SEOHead } from "@/components/SEOHead";
import { Home } from "./pages/Home";
import { Properties } from "./pages/Properties";
import { Agents } from "./pages/Agents";
import { About } from "./pages/About";
import { Services } from "./pages/Services";
import { Contact } from "./pages/Contact";
import { Gallery } from "./pages/Gallery";
import { News } from "./pages/News";
import { NewsDetail } from "./pages/NewsDetail";
import { PropertyDetails } from "./pages/PropertyDetails";
import { Auth } from "./pages/Auth";
import { CustomerDashboard } from "./components/dashboard/CustomerDashboard";
import { AdminDashboard } from "./components/dashboard/AdminDashboard";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <OrganizationProvider>
        <SEOHead />
        <AuthProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          <Routes>
            {/* Public Routes with Layout */}
            <Route path="/" element={<Layout><Home /></Layout>} />
            <Route path="/properties" element={<Layout><Properties /></Layout>} />
            <Route path="/property/:id" element={<Layout><PropertyDetails /></Layout>} />
            <Route path="/agents" element={<Layout><Agents /></Layout>} />
            <Route path="/about" element={<Layout><About /></Layout>} />
            <Route path="/services" element={<Layout><Services /></Layout>} />
            <Route path="/contact" element={<Layout><Contact /></Layout>} />
            <Route path="/gallery" element={<Layout><Gallery /></Layout>} />
            <Route path="/news" element={<Layout><News /></Layout>} />
            <Route path="/news/:slug" element={<Layout><NewsDetail /></Layout>} />

            {/* Auth Page (No Layout) */}
            <Route path="/auth" element={<Auth />} />

            {/* Protected Dashboard Routes (No Layout) */}
            <Route 
              path="/customer-dashboard" 
              element={
                <ProtectedRoute requireRole="customer">
                  <CustomerDashboard />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="/admin-dashboard" 
              element={
                <ProtectedRoute requireRole="admin">
                  <AdminDashboard />
                </ProtectedRoute>
              } 
            />
            
            {/* Catch-all route */}
            <Route path="*" element={<Layout><NotFound /></Layout>} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </OrganizationProvider>
  </TooltipProvider>
</QueryClientProvider>
);

export default App;
