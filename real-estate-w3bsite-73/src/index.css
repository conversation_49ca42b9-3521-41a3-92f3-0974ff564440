@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar utilities */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Premium Real Estate Design System - All colors MUST be HSL */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Real Estate Brand Colors */
    --navy-primary: 223 84% 25%;
    --navy-light: 223 54% 35%;
    --navy-dark: 223 94% 15%;
    --gold-primary: 45 93% 47%;
    --gold-light: 45 100% 65%;
    --gold-dark: 45 100% 35%;
    
    /* Neutral Palette */
    --gray-light: 210 20% 96%;
    --gray-medium: 215 16% 47%;
    --gray-dark: 222 47% 11%;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--navy-dark)) 0%, hsl(var(--navy-primary)) 100%);
    --gradient-gold: linear-gradient(135deg, hsl(var(--gold-dark)) 0%, hsl(var(--gold-primary)) 100%);
    --gradient-overlay: linear-gradient(to bottom, transparent 0%, rgba(30, 58, 138, 0.7) 100%);
    
    /* Shadows */
    --shadow-elegant: 0 10px 40px -10px hsl(var(--navy-primary) / 0.3);
    --shadow-gold: 0 4px 20px hsl(var(--gold-primary) / 0.2);
    --shadow-subtle: 0 2px 10px hsl(var(--gray-dark) / 0.1);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease-out;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: var(--navy-primary);
    --primary-foreground: 0 0% 100%;

    --secondary: var(--gray-light);
    --secondary-foreground: var(--navy-dark);

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: var(--gold-primary);
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Scroll Animations */
@layer components {
  .scroll-animate {
    @apply opacity-0 translate-y-8 transition-all duration-700;
  }
  
  .scroll-animate.animate-in {
    @apply opacity-100 translate-y-0;
  }
}