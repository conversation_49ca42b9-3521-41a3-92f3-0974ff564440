import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface AuthPromptConfig {
  feature: 'save' | 'contact' | 'schedule' | 'general';
  title?: string;
  description?: string;
  onSuccess?: () => void;
}

export const useAuthPrompt = () => {
  const { user } = useAuth();
  const [isPromptOpen, setIsPromptOpen] = useState(false);
  const [promptConfig, setPromptConfig] = useState<AuthPromptConfig>({
    feature: 'general'
  });

  const requireAuth = (config: AuthPromptConfig, callback?: () => void) => {
    if (user) {
      // User is authenticated, execute callback immediately
      if (callback) callback();
      if (config.onSuccess) config.onSuccess();
      return true;
    } else {
      // User is not authenticated, show prompt
      setPromptConfig(config);
      setIsPromptOpen(true);
      return false;
    }
  };

  const closePrompt = () => {
    setIsPromptOpen(false);
  };

  const handleLogin = () => {
    setIsPromptOpen(false);
    window.location.href = '/auth?tab=login';
  };

  const handleRegister = () => {
    setIsPromptOpen(false);
    window.location.href = '/auth?tab=register';
  };

  return {
    isPromptOpen,
    promptConfig,
    requireAuth,
    closePrompt,
    handleLogin,
    handleRegister,
    isAuthenticated: !!user
  };
};
