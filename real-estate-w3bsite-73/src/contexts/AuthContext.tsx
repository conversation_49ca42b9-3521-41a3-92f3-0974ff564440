import { postReq } from '@/apiService';
import React, { createContext, useContext, useState, useEffect } from 'react';

export interface User {
  id: string;
  email: string;
  name: string;
  roles: 'customer' | 'admin';
  phone?: string;
  avatar?: string;
  username?: string; // Add username for API compatibility
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: Omit<User, 'id'> & { password: string }) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
}

// API Response interfaces
interface LoginResponse {
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    is_superuser?: boolean;
    is_staff?: boolean;
    is_active: boolean;
    roles?: 'customer' | 'admin'; // New field from serializer
  };
  token: string;
  refresh_token: string;
}

interface RegisterResponse {
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    phone_number?: string;
    is_superuser?: boolean;
    is_staff?: boolean;
    is_active: boolean;
    roles?: 'customer' | 'admin'; // New field from serializer
  };
  token: string;
  refresh_token: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    // Check for stored user on app load
    const storedUser = localStorage.getItem('user');
    const storedToken = localStorage.getItem('authToken');
    if (storedUser && storedToken) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('user');
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
      }
    }
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      // Convert email to username for API call (part before @)
      const username = email.includes('@') ? email.split('@')[0] : email;
      
      const response = await postReq('auth/login/', {
        username,
        password
      });

      if (response.status === '200' && response.data) {
        const loginData: LoginResponse = response.data;

        console.log('🔍 Login API Response:', loginData);

        // Determine roles - prefer the roles field from serializer, fallback to is_superuser/is_staff
        let roles: 'customer' | 'admin' = 'customer';
        if (loginData.user.roles) {
          roles = loginData.user.roles;
          console.log('✅ roles determined from serializer field:', roles);
        } else if (loginData.user.is_superuser || loginData.user.is_staff) {
          roles = 'admin';
          console.log('✅ roles determined from is_superuser/is_staff:', roles);
        } else {
          console.log('✅ roles defaulted to customer');
        }

        // Transform API response to match your User interface
        const transformedUser: User = {
          id: loginData.user.id,
          email: loginData.user.email,
          name: `${loginData.user.first_name} ${loginData.user.last_name}`.trim(),
          roles: roles,
          phone: loginData.user.phone_number,
          username: loginData.user.username
        };



        // Store tokens and user data
        localStorage.setItem('authToken', loginData.token);
        if (loginData.refresh_token) {
          localStorage.setItem('refreshToken', loginData.refresh_token);
        }
        localStorage.setItem('user', JSON.stringify(transformedUser));

        setUser(transformedUser);
        return true;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  };

  const register = async (userData: Omit<User, 'id'> & { password: string }): Promise<boolean> => {
    try {
      // Split name into first_name and last_name
      const nameParts = userData.name.trim().split(' ');
      const first_name = nameParts[0];
      const last_name = nameParts.slice(1).join(' ') || '';

      // Create username from email (part before @)
      const username = userData.email.split('@')[0];

      const requestData = {
        username,
        email: userData.email,
        first_name,
        last_name,
        phone_number: userData.phone || '',
        role: userData.roles || 'customer',
        password: userData.password,
        password_confirm: userData.password
      };

      const response = await postReq('auth/register/', requestData);

      if ((response.status === '200' || response.status === '201') && response.data) {
        const registerData: RegisterResponse = response.data;

        // Determine roles - prefer the roles field from serializer, fallback to is_superuser/is_staff
        let roles: 'customer' | 'admin' = 'customer';
        if (registerData.user.roles) {
          roles = registerData.user.roles;
        } else if (registerData.user.is_superuser || registerData.user.is_staff) {
          roles = 'admin';
        }

        // Transform API response to match your User interface
        const transformedUser: User = {
          id: registerData.user.id,
          email: registerData.user.email,
          name: `${registerData.user.first_name} ${registerData.user.last_name}`.trim(),
          roles: roles,
          phone: registerData.user.phone_number,
          username: registerData.user.username
        };



        // Store tokens and user data
        localStorage.setItem('authToken', registerData.token);
        if (registerData.refresh_token) {
          localStorage.setItem('refreshToken', registerData.refresh_token);
        }
        localStorage.setItem('user', JSON.stringify(transformedUser));

        setUser(transformedUser);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      register,
      logout,
      isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  );
};