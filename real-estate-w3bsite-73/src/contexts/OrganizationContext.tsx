import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getReq } from '@/apiService';
import { updateMetaTags } from '@/utils/metaUpdater';

interface OrganizationData {
  id: number;
  name: string;
  description: string;
  logo: string;
  phone: string;
  email: string;
  address: string;
  whatsapp: string;
  facebook: string;
  instagram: string;
  linkedin: string;
  twitter: string;
  created_at: string;
  updated_at: string;
}

interface OrganizationContextType {
  organization: OrganizationData | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

interface OrganizationProviderProps {
  children: ReactNode;
}

export const OrganizationProvider: React.FC<OrganizationProviderProps> = ({ children }) => {
  const [organization, setOrganization] = useState<OrganizationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrganization = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await getReq('organization/');

      if (data && typeof data === 'object') {
        setOrganization(data);
        // Update meta tags with real organization data
        updateMetaTags(data);
      } else {
        setError('Invalid organization data received');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch organization data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrganization();
  }, []);

  const value: OrganizationContextType = {
    organization,
    loading,
    error,
    refetch: fetchOrganization,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
};

export const useOrganization = (): OrganizationContextType => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};
