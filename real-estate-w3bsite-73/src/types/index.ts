// Django Model Interfaces for Frontend

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'customer' | 'admin';
  phone?: string;
  avatar?: string;
  is_active: boolean;
  date_joined: string;
}

export interface Organization {
  id?: string;
  name?: string;
  description?: string;
  logo?: string;
  phone?: string;
  email?: string;
  address?: string;
  website?: string;
  whatsapp?: string;
  facebook?: string;
  instagram?: string;
  linkedin?: string;
  twitter?: string;
}

export interface AboutUs {
  id: string;
  title: string;
  content: string;
  image1: string;
  image2: string;
  vision: string;
  mission: string;
  is_active: boolean;
}

export interface Achievements {
  id: string;
  properties_sold: number;
  sales_volume: number;
  years_experience: number;
  clients_served: number;
}

export interface Service {
  id: string;
  title: string;
  description: string;
  icon: string;
  is_active: boolean;
  order: number;
}

export interface Journey {
  id: string;
  year: number;
  title: string;
  description: string;
  order: number;
}

export interface HeroSection {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  button_text: string;
  button_link: string;
  is_active: boolean;
  order: number;
}

export interface PropertyType {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
}

export interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  location: string;
  address: string;
  bedrooms?: number; // Now optional since it's nullable
  bathrooms: number;
  area: number;
  area_unit?: string;
  formatted_area?: string;
  area_in_sqft?: number;

  // Property purpose (Land or Rent)
  property_purpose?: 'land' | 'rent';
  purpose_display?: string;

  // Sophisticated land area system (Ropani-Aana-Paisa-Daam)
  land_ropani?: number;
  land_aana?: number;
  land_paisa?: number;
  land_daam?: number;
  formatted_land_area?: string;
  land_area_display?: string;

  // Google Maps embed functionality
  google_maps_embed_url?: string;
  google_maps_embed_src?: string;

  latitude?: number;
  longitude?: number;
  images?: string[];
  property_type: string;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Agent {
  id: string;
  name: string;
  specialty: string;
  phone: string;
  email: string;
  profile_picture: string;
  bio: string;
  is_active: boolean;
  user?: string;
  user_details?: {
    first_name?: string;
    last_name?: string;
    username?: string;
    email?: string;
  };
}

export interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
  message: string;
  status: 'new' | 'in_progress' | 'resolved' | 'closed';
  assigned_agent: string;
  created_at: string;
  updated_at: string;
}

export interface Inquiry {
  id: string;
  property_id: string;
  customer_id: string;
  status: 'pending' | 'contacted' | 'scheduled' | 'closed';
  message: string;
  created_at: string;
  customer_name: string;
  customer_email: string;
  property_title: string;
}

export interface Visit {
  id: string;
  property_id: string;
  customer_id: string;
  date: string;
  agent_name?: string;
  time: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  property_title: string;
  notes: string;
}

// Additional interfaces for Gallery and News
export interface Gallery {
  id: string;
  title: string;
  description: string;
  image: string;
  is_active: boolean;
  created_at: string;
}

export interface NewsCategory {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
}

export interface News {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image: string;
  category: string;
  slug: string;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Team interface
export interface Team {
  id: string;
  name: string;
  position: string;
  bio: string;
  email: string;
  phone: string;
  image: string | null;
  linkedin: string;
  twitter: string;
  facebook: string;
  instagram: string;
  order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
