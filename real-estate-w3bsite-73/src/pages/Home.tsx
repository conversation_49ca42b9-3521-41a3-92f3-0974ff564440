import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import heroImage from '@/assets/hero-property.jpg';
import  HeroCarousel  from '@/components/HeroCarousel';
import { PropertyCard } from '@/components/PropertyCard';
import { ServiceCard } from '@/components/ServiceCard';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { getReq } from '@/apiService';

export const Home = () => {
  useScrollAnimation();
  
  const [featuredProperties, setFeaturedProperties] = useState([]);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [servicesLoading, setServicesLoading] = useState(false);
  const [error, setError] = useState(null);
  const [servicesError, setServicesError] = useState(null);

  useEffect(() => {
    // Fetch featured properties
    setLoading(true);
    getReq('properties/')
      .then((data) => {

        // getReq already unwraps paginated responses, so data should be an array
        if (data === null) {
          setError('Failed to load properties. API returned null.');
          setFeaturedProperties([]);
          return;
        }

        if (!Array.isArray(data)) {
          setError('Invalid data format received from API.');
          setFeaturedProperties([]);
          return;
        }

        if (data.length === 0) {
          setError('No properties found.');
          setFeaturedProperties([]);
          return;
        }

        // Transform backend data to match frontend expectations
        const transformedProperties = data.map(property => {
          // Find primary image or use first image
          let imageUrl = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=600";
          if (property.images && property.images.length > 0) {
            const primaryImage = property.images.find(img => img.is_primary);
            const selectedImage = primaryImage || property.images[0];
            imageUrl = selectedImage.image;
          }

          return {
            id: property.id,
            title: property.title,
            location: property.location,
            address: property.address,
            price: `Rs. ${parseFloat(property.price).toLocaleString()}`,
            image: imageUrl,
            beds: property.bedrooms,
            baths: property.bathrooms,
            sqft: property.area,
            type: property.property_type_name,
            featured: property.is_featured,
            description: property.description
          };
        });

        // Filter featured properties or take first 3
        const featured = transformedProperties.filter(p => p.featured);
        const finalFeatured = featured.length > 0 ? featured.slice(0, 3) : transformedProperties.slice(0, 3);

        setFeaturedProperties(finalFeatured);
        setError(null);
      })
      .catch((err) => {
        setError('Failed to load properties.');
        setFeaturedProperties([]);
      })
      .finally(() => {
        setLoading(false);
      });

    // Fetch services
    setServicesLoading(true);
    getReq('services/')
      .then((data) => {
        if (Array.isArray(data)) {
          setServices(data.slice(0, 4)); // Show first 4 services
        } else if (data === null) {
          setServicesError('Failed to load services. API returned null.');
        } else {
          setServicesError('No services found.');
          setServices([]);
        }
      })
      .catch((err) => {
        setServicesError('Failed to load services.');
      })
      .finally(() => {
        setServicesLoading(false);
      });
  }, []);

  return (
    <div className="min-h-screen">
      {/* Hero Carousel Section */}
      <section className="relative h-screen overflow-hidden">
        <HeroCarousel />
        
        <div className="absolute inset-0 bg-gradient-to-b from-navy-dark/50 via-navy-primary/60 to-navy-dark/80 z-10" />
        
        <div className="relative z-20 h-full flex items-center justify-center">
          <div className="container mx-auto px-4 lg:px-8 text-center text-white">
            <div className="max-w-4xl mx-auto animate-fade-in">
              <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                Find Your
                <span className="block bg-gradient-to-r from-gold-light to-gold-primary bg-clip-text text-transparent">
                  Dream Property
                </span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-light max-w-2xl mx-auto">
                Discover luxury real estate with Premier Properties. We specialize in premium homes 
                and exceptional service for discerning clients.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button variant="premium" size="lg" className="text-lg px-8 py-4">
                  <Link to="/properties">Explore Properties</Link>
                </Button>
                <Button variant="hero" size="lg" className="text-lg px-8 py-4">
                  <Link to="/contact">Schedule Consultation</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2" />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="animate-fade-in">
              <h3 className="text-4xl font-bold text-navy-primary mb-2">500+</h3>
              <p className="text-gray-medium">Properties Sold</p>
            </div>
            <div className="animate-fade-in">
              <h3 className="text-4xl font-bold text-navy-primary mb-2">15+</h3>
              <p className="text-gray-medium">Years Experience</p>
            </div>
            <div className="animate-fade-in">
              <h3 className="text-4xl font-bold text-navy-primary mb-2">98%</h3>
              <p className="text-gray-medium">Client Satisfaction</p>
            </div>
            <div className="animate-fade-in">
              <h3 className="text-4xl font-bold text-navy-primary mb-2">$2.1B</h3>
              <p className="text-gray-medium">Total Sales Volume</p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="py-20 bg-gray-light">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Featured Properties
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              Discover our handpicked selection of luxury properties in prime locations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProperties.length > 0 ? (
              featuredProperties.map((property) => (
                <PropertyCard
                  key={property.id}
                  property={property}
                />
              ))
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-medium">No featured properties available</p>
              </div>
            )}
          </div>

          <div className="text-center mt-12">
            <Button variant="navy" size="lg">
              <Link to="/properties">View All Properties</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Enhanced Services Section */}
      <section className="py-20 bg-white scroll-animate">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Our Services
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              Comprehensive real estate services tailored to your unique needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <ServiceCard 
                key={service.title} 
                service={service} 
                index={index} 
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-navy-primary to-navy-light text-white">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Find Your Perfect Property?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto text-gray-light">
            Let our experienced team guide you through every step of your real estate journey
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button variant="premium" size="lg" className="text-lg px-8 py-4">
              <Link to="/contact">Get Started Today</Link>
            </Button>
            <Button variant="hero" size="lg" className="text-lg px-8 py-4">
              <Link to="/agents">Meet Our Team</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};