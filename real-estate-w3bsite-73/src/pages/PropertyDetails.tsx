import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { getReq } from '@/apiService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SavePropertyButton } from '@/components/property/SavePropertyButton';
import { ContactAgentButton } from '@/components/property/ContactAgentButton';
import {
  getBestAreaDisplay,
  hasLandAreaInfo,
  hasGoogleMapsEmbed,
  getGoogleMapsEmbedUrl
} from '@/utils/propertyUtils';
import {
  Bed,
  Bath,
  Square,
  MapPin,
  Car,
  ChevronLeft,
  ChevronRight,
  Phone,
  Mail
} from 'lucide-react';

export const PropertyDetails = () => {
  const { id } = useParams();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [property, setProperty] = useState<any>(null);
  const [agent, setAgent] = useState<any>(null);
  const [similarProperties] = useState([]);

  useEffect(() => {
    if (id) {
      setLoading(true);
      getReq(`properties/${id}/`)
        .then((data) => {
          if (data && typeof data === 'object') {
            // Transform backend data to match frontend expectations
            const transformedProperty = {
              id: data.id,
              title: data.title,
              location: data.location,
              address: data.address,
              price: `Rs. ${parseFloat(data.price).toLocaleString()}`,
              beds: data.bedrooms,
              baths: data.bathrooms,
              property_purpose: data.property_purpose,
              purpose_display: data.purpose_display,
              sqft: data.area,
              area: data.area,
              area_unit: data.area_unit,
              formatted_area: data.formatted_area,
              formatted_land_area: data.formatted_land_area,
              land_area_display: data.land_area_display,
              google_maps_embed_url: data.google_maps_embed_url,
              google_maps_embed_src: data.google_maps_embed_src,
              type: data.property_type_name,
              status: data.is_active ? 'For Sale' : 'Sold',
              description: data.description,
              latitude: data.latitude,
              longitude: data.longitude,
              yearBuilt: "2020", // Mock data - not in backend
              garage: 2, // Mock data - not in backend
              images: data.images && data.images.length > 0
                ? data.images.map((img: any) => img.image)
                : ["https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=800"],
              features: [
                "Gourmet Kitchen",
                "Master Suite",
                "Pool & Spa",
                "3-Car Garage",
                "Home Theater",
                "Wine Cellar"
              ] // Mock features - not in backend
            };
            setProperty(transformedProperty);

            // Fetch a default agent since properties don't have assigned agents yet
            fetchDefaultAgent();
          } else {
            // Property not found
            setProperty(null);
            setAgent(null);
          }
        })
        .catch((error) => {
          console.error('Error fetching property:', error);
          setProperty(null);
          setAgent(null);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id]);

  // Fetch default agent
  const fetchDefaultAgent = async () => {
    try {
      const agentsData = await getReq('agents/');
      if (agentsData && Array.isArray(agentsData) && agentsData.length > 0) {
        // Use the first active agent as default
        const defaultAgent = agentsData.find(agent => agent.is_active) || agentsData[0];
        setAgent({
          name: defaultAgent.full_name || defaultAgent.name,
          title: defaultAgent.title || defaultAgent.specialty,
          phone: defaultAgent.phone || defaultAgent.whatsapp || "(*************",
          email: defaultAgent.email,
          image: defaultAgent.photo || defaultAgent.profile_picture || "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150"
        });
      } else {
        // No agents available
        setAgent(null);
      }
    } catch (error) {
      console.error('Error fetching agents:', error);
      setAgent(null);
    }
  };



  
  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % (property?.images?.length || 1));
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + (property?.images?.length || 1)) % (property?.images?.length || 1));
  };




  if (loading) {
    return (
      <div className="min-h-screen bg-gray-light flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-light flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-navy-dark mb-4">Property not found</h2>
          <Link to="/properties" className="text-navy-primary hover:underline">
            Back to Properties
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>

      <div className="min-h-screen bg-gray-light">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 lg:px-8 py-4">
          <div className="flex items-center gap-2 text-sm text-gray-medium">
            <Link to="/" className="hover:text-navy-primary">Home</Link>
            <span>/</span>
            <Link to="/properties" className="hover:text-navy-primary">Properties</Link>
            <span>/</span>
            <span className="text-navy-dark">{property.title}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <Card className="overflow-hidden">
              <div className="relative h-96 md:h-[500px]">
                <img
                  src={property?.images?.[currentImageIndex]}
                  alt={property?.title}
                  className="w-full h-full object-cover"
                />
                
                {/* Navigation Arrows */}
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 hover:bg-white transition-colors"
                >
                  <ChevronLeft size={20} />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/80 hover:bg-white transition-colors"
                >
                  <ChevronRight size={20} />
                </button>

                {/* Image Counter */}
                <div className="absolute top-4 right-4 bg-navy-dark/80 text-white px-3 py-1 rounded-full text-sm">
                  {currentImageIndex + 1} / {property?.images?.length || 1}
                </div>
              </div>

              {/* Thumbnail Navigation */}
              <div className="p-4 bg-white">
                 <div className="flex gap-2 overflow-x-auto">
                   {property?.images?.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                        index === currentImageIndex ? 'border-gold-primary' : 'border-gray-300'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`View ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                     </button>
                   )) || []}
                 </div>
              </div>
            </Card>

            {/* Property Information */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                     <CardTitle className="text-3xl text-navy-dark mb-2">{property?.title}</CardTitle>
                     <CardDescription className="flex items-center gap-1 text-lg">
                       <MapPin size={16} />
                       {property?.location}
                     </CardDescription>
                   </div>
                   <div className="text-right">
                     <div className="text-3xl font-bold text-navy-primary mb-2">{property?.price}</div>
                     <Badge variant="secondary">{property?.status}</Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                  <div className="flex items-center gap-2">
                    <MapPin className="text-gold-primary" size={20} />
                    <span className="text-gray-dark">{property.purpose_display || property.property_purpose || 'Land'}</span>
                  </div>
                  {property.property_purpose === 'rent' && property.beds && (
                    <div className="flex items-center gap-2">
                      <Bed className="text-gold-primary" size={20} />
                      <span className="text-gray-dark">{property.beds} Bedrooms</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Bath className="text-gold-primary" size={20} />
                    <span className="text-gray-dark">{property.baths} Bathrooms</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Square className="text-gold-primary" size={20} />
                    <span className="text-gray-dark">
                      {getBestAreaDisplay(property)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Car className="text-gold-primary" size={20} />
                    <span className="text-gray-dark">{property.garage} Garage</span>
                  </div>
                </div>

                <div className="prose max-w-none">
                  <p className="text-gray-dark leading-relaxed">{property.description}</p>
                </div>
              </CardContent>
            </Card>

            {/* Property Specifications */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-navy-dark">Property Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Property Type:</span>
                      <span className="text-gray-dark font-medium">{property.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Property Purpose:</span>
                      <span className="text-gray-dark font-medium">{property.purpose_display || property.property_purpose || 'Land'}</span>
                    </div>
                    {property.property_purpose === 'rent' && property.beds && (
                      <div className="flex justify-between">
                        <span className="text-gray-medium">Bedrooms:</span>
                        <span className="text-gray-dark font-medium">{property.beds}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Bathrooms:</span>
                      <span className="text-gray-dark font-medium">{property.baths}</span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Area:</span>
                      <span className="text-gray-dark font-medium">{getBestAreaDisplay(property)}</span>
                    </div>
                    {hasLandAreaInfo(property) && property.land_area_display && (
                      <div className="flex justify-between">
                        <span className="text-gray-medium">Land Area:</span>
                        <span className="text-gray-dark font-medium">{property.land_area_display}</span>
                      </div>
                    )}
                    {hasLandAreaInfo(property) && property.formatted_land_area && property.formatted_land_area !== '0-0-0-0' && (
                      <div className="flex justify-between">
                        <span className="text-gray-medium">Land Format:</span>
                        <span className="text-gray-dark font-medium">{property.formatted_land_area}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Garage Spaces:</span>
                      <span className="text-gray-dark font-medium">{property.garage}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Status:</span>
                      <span className="text-gray-dark font-medium">{property.status}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-medium">Price:</span>
                      <span className="text-navy-primary font-bold">{property.price}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-navy-dark">Features & Amenities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {property.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gold-primary rounded-full" />
                      <span className="text-gray-dark">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Location Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-navy-dark">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="text-gold-primary mt-1" size={20} />
                    <div>
                      <h3 className="font-semibold text-navy-dark mb-1"> Address</h3>
                      <p className="text-gray-dark">{property.location}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <MapPin className="text-gold-primary mt-1" size={20} />
                    <div>
                      <h3 className="font-semibold text-navy-dark mb-1">Area</h3>
                      <p className="text-gray-dark">{property.address}</p>
                    </div>
                  </div>

                  {property.latitude && property.longitude && (
                    <div className="flex items-start gap-3">
                      <MapPin className="text-gold-primary mt-1" size={20} />
                      <div>
                        <h3 className="font-semibold text-navy-dark mb-1">Coordinates</h3>
                        <p className="text-gray-dark">
                          Lat: {property.latitude}, Long: {property.longitude}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Google Maps Embed */}
                  {hasGoogleMapsEmbed(property) && (
                    <div className="mt-6">
                      <h3 className="font-semibold text-navy-dark mb-3">Map Location</h3>
                      <div className="w-full h-64 md:h-80 rounded-lg overflow-hidden border border-gray-200">
                        <iframe
                          src={getGoogleMapsEmbedUrl(property) || ''}
                          width="100%"
                          height="100%"
                          style={{ border: 0 }}
                          allowFullScreen
                          loading="lazy"
                          referrerPolicy="no-referrer-when-downgrade"
                          title="Property Location Map"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Agent */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl text-navy-dark">Contact Agent</CardTitle>
              </CardHeader>
              <CardContent>
                {agent && (
                  <div className="flex items-center gap-4 mb-6">
                    <img
                      src={agent.image}
                      alt={agent.name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="font-semibold text-navy-dark">{agent.name}</h3>
                      <p className="text-gray-medium text-sm">{agent.title}</p>
                    </div>
                  </div>
                )}

                {agent && (
                  <>
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-2">
                        <Phone size={16} className="text-gold-primary" />
                        <span className="text-gray-dark">{agent.phone}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Mail size={16} className="text-gold-primary" />
                        <span className="text-gray-dark">{agent.email}</span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <ContactAgentButton
                        propertyId={id}
                        propertyTitle={property?.title}
                        agentId={agent?.id}
                        className="w-full"
                        variant="default"
                      >
                        Send Message
                      </ContactAgentButton>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardContent className="p-4 space-y-3">
                <Button variant="navy" className="w-full">
                  Schedule a Tour
                </Button>
                <Button variant="elegant" className="w-full">
                  Request Information
                </Button>
                <SavePropertyButton
                  propertyId={id}
                  className="w-full"
                  variant="ghost"
                  showText={true}
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Similar Properties */}
        <div className="mt-16">
          <h2 className="text-3xl font-bold text-navy-dark mb-8">Similar Properties</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {similarProperties.map((similarProperty) => (
              <Card key={similarProperty.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="flex">
                  <div className="w-1/3">
                    <img
                      src={similarProperty.image}
                      alt={similarProperty.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="w-2/3 p-4">
                    <h3 className="text-lg font-semibold text-navy-dark mb-1">{similarProperty.title}</h3>
                    <p className="text-gray-medium text-sm mb-2">{similarProperty.location}</p>
                    <p className="text-xl font-bold text-navy-primary mb-3">{similarProperty.price}</p>
                    <div className="flex gap-4 text-sm text-gray-medium">
                      <span>{similarProperty.beds} beds</span>
                      <span>{similarProperty.baths} baths</span>
                      <span>{similarProperty.sqft} sqft</span>
                    </div>
                    <Link to={`/property/${similarProperty.id}`}>
                      <Button variant="elegant" size="sm" className="mt-3">
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};