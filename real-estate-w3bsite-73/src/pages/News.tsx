import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getReq } from '@/apiService';
import { Calendar, User, ArrowRight } from 'lucide-react';

interface NewsCategory {
  id: number;
  name: string;
  description: string;
}

interface NewsArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image: string | null;
  category_details: NewsCategory | null;
  is_featured: boolean;
  published_at: string;
}

export const News = () => {
  const [news, setNews] = useState<NewsArticle[]>([]);
  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [featuredOnly, setFeaturedOnly] = useState(false);

  useEffect(() => {
    // Fetch categories
    getReq('news/categories/')
      .then((data) => {
        if (Array.isArray(data)) {
          setCategories(data);
        }
      })
      .catch((err) => {
        console.error('News.tsx: Error fetching categories:', err);
      });
  }, []);

  useEffect(() => {
    setLoading(true);
    let url = 'news/';
    const params = new URLSearchParams();
    
    if (selectedCategory) {
      params.append('category', selectedCategory.toString());
    }
    if (featuredOnly) {
      params.append('featured', 'true');
    }
    
    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    getReq(url)
      .then((data) => {
        if (Array.isArray(data)) {
          setNews(data);
        } else if (data === null) {
          setError('Failed to load news. API returned null.');
          setNews([]);
        } else {
          setError('No news articles found.');
          setNews([]);
        }
      })
      .catch((err) => {
        setError('Failed to load news.');
        setNews([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [selectedCategory, featuredOnly]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-light flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-navy-primary to-navy-light text-white py-20">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            News & Insights
          </h1>
          <p className="text-xl text-gray-light max-w-2xl mx-auto">
            Stay updated with the latest real estate market trends, tips, and company news
          </p>
        </div>
      </section>

      {/* Filters */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="flex flex-wrap gap-4 items-center justify-center">
            <Button
              variant={selectedCategory === null && !featuredOnly ? "default" : "outline"}
              onClick={() => {
                setSelectedCategory(null);
                setFeaturedOnly(false);
              }}
            >
              All News
            </Button>
            <Button
              variant={featuredOnly ? "default" : "outline"}
              onClick={() => {
                setSelectedCategory(null);
                setFeaturedOnly(true);
              }}
            >
              Featured
            </Button>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                onClick={() => {
                  setSelectedCategory(category.id);
                  setFeaturedOnly(false);
                }}
              >
                {category.name}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* News Content */}
      <section className="py-20">
        <div className="container mx-auto px-4 lg:px-8">
          {error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">{error}</p>
            </div>
          ) : news.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-medium text-lg">No news articles available at the moment.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {news.map((article) => (
                <Card key={article.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group">
                  {article.featured_image && (
                    <div className="relative aspect-video overflow-hidden">
                      <img
                        src={article.featured_image}
                        alt={article.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=600";
                        }}
                      />
                      {article.is_featured && (
                        <Badge className="absolute top-4 left-4 bg-gold-primary text-white">
                          Featured
                        </Badge>
                      )}
                    </div>
                  )}
                  
                  <CardHeader>
                    <div className="flex items-center gap-2 text-sm text-gray-medium mb-2">
                      <Calendar size={16} />
                      <span>{formatDate(article.published_at)}</span>
                      {article.category_details && (
                        <>
                          <span>•</span>
                          <Badge variant="secondary">{article.category_details.name}</Badge>
                        </>
                      )}
                    </div>
                    <CardTitle className="text-xl text-navy-dark group-hover:text-navy-primary transition-colors">
                      {article.title}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-gray-medium mb-4">
                      {article.excerpt || truncateContent(article.content)}
                    </p>
                    <Link to={`/news/${article.slug}`}>
                      <Button variant="outline" className="group-hover:bg-navy-primary group-hover:text-white transition-colors">
                        Read More
                        <ArrowRight size={16} className="ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};
