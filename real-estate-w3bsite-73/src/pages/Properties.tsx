import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DistrictCombobox } from '@/components/ui/district-combobox';
import { PropertyCard } from '@/components/PropertyCard';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { getReq } from '@/apiService';

// Extended property interface for internal use with filtering
interface ExtendedProperty {
  id: number;
  title: string;
  location: string;
  address: string;
  price: string;
  numericPrice: number; // For filtering
  image: string;
  beds: number;
  baths: number;
  sqft: string;
  area?: number;
  area_unit?: string;
  formatted_area?: string;
  type: string;
  featured: boolean;
  description: string;
  latitude?: number | null;
  longitude?: number | null;
  created_at: string;
}

export const Properties = () => {
  useScrollAnimation();

  const [searchFilters, setSearchFilters] = useState({
    location: '',
    minPrice: '',
    maxPrice: '',
    propertyType: '',
    bedrooms: ''
  });

  const [properties, setProperties] = useState<ExtendedProperty[]>([]);
  const [propertyTypes, setPropertyTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);

    getReq('properties/')
      .then((data) => {
        if (data === null || data === undefined) {
          setError('Failed to load properties. API returned null.');
          setProperties([]);
          return;
        }

        // Handle different response formats
        let propertiesArray = [];
        if (Array.isArray(data)) {
          propertiesArray = data;
        } else if (data.results && Array.isArray(data.results)) {
          propertiesArray = data.results;
        } else if (data.data && Array.isArray(data.data)) {
          propertiesArray = data.data;
        } else {
          setError('Invalid data format received from API.');
          setProperties([]);
          return;
        }

        if (propertiesArray.length === 0) {
          setError('No properties found.');
          setProperties([]);
          return;
        }

        console.log('Properties array:', propertiesArray);

        // Transform backend data to match frontend expectations
        const transformedProperties = propertiesArray.map(property => {
          console.log('Processing property:', property);

          // Handle image URL
          let imageUrl = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=600";
          if (property.images && Array.isArray(property.images) && property.images.length > 0) {
            const primaryImage = property.images.find((img: any) => img.is_primary);
            const selectedImage = primaryImage || property.images[0];
            imageUrl = selectedImage.image || selectedImage.url || selectedImage;
          }

          // Handle price formatting
          let formattedPrice = property.price;
          let numericPrice = 0;

          if (typeof property.price === 'number') {
            numericPrice = property.price;
            formattedPrice = `Rs. ${property.price.toLocaleString()}`;
          } else if (typeof property.price === 'string') {
            // Extract numeric value from string
            const cleanPrice = property.price.replace(/[^\d.]/g, '');
            numericPrice = parseFloat(cleanPrice) || 0;

            if (!property.price.startsWith('Rs.')) {
              formattedPrice = `Rs. ${numericPrice.toLocaleString()}`;
            } else {
              formattedPrice = property.price;
            }
          }

          return {
            id: property.id || Math.random(),
            title: property.title || property.name || 'Untitled Property',
            location: property.location || property.address || 'Location not specified',
            address: property.address || property.location || '',
            price: formattedPrice,
            numericPrice: numericPrice, // Store numeric price for filtering
            image: imageUrl,
            beds: property.bedrooms || property.beds || 0,
            baths: property.bathrooms || property.baths || 0,
            sqft: property.area || property.sqft || property.square_feet || '0',
            area: property.area,
            area_unit: property.area_unit,
            formatted_area: property.formatted_area,
            type: property.property_type_name || property.property_type || property.type || 'Property',
            featured: property.is_featured || false,
            description: property.description || '',
            latitude: property.latitude || null,
            longitude: property.longitude || null,
            created_at: property.created_at || new Date().toISOString()
          };
        });

        setProperties(transformedProperties);
        setError(null);
      })
      .catch(() => {
        setError('Failed to load properties. Please try again.');
        setProperties([]);
      })
      .finally(() => {
        setLoading(false);
      });

    // Fetch property types for filters
    getReq('property-types/')
      .then((data) => {
        if (Array.isArray(data)) {
          setPropertyTypes(data);
        } else if (data && data.results && Array.isArray(data.results)) {
          setPropertyTypes(data.results);
        }
      })
      .catch(() => {
        // Silently fail for property types as it's not critical
      });
  }, []);

  const filteredProperties = properties.filter(property => {
    const matchesLocation = !searchFilters.location ||
      property.location.toLowerCase().includes(searchFilters.location.toLowerCase());

    const matchesType = !searchFilters.propertyType ||
      property.type.toLowerCase().includes(searchFilters.propertyType.toLowerCase());

    const matchesBedrooms = !searchFilters.bedrooms ||
      property.beds >= parseInt(searchFilters.bedrooms);

    // Price filtering logic - use the stored numeric price
    const propertyPrice = property.numericPrice || 0;
    const minPrice = searchFilters.minPrice ? parseFloat(searchFilters.minPrice) : 0;
    const maxPrice = searchFilters.maxPrice ? parseFloat(searchFilters.maxPrice) : Infinity;

    const matchesPrice = propertyPrice >= minPrice && propertyPrice <= maxPrice;

    return matchesLocation && matchesType && matchesBedrooms && matchesPrice;
  });

  const featuredProperties = filteredProperties.filter(p => p.featured);

  return (
    <div className="min-h-screen bg-gray-light">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-navy-dark to-navy-primary text-white py-20">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-gold-light to-white bg-clip-text text-transparent">
            Luxury Properties
          </h1>
          <p className="text-xl text-gray-light max-w-2xl mx-auto">
            Discover exceptional properties in the most desirable locations
          </p>
        </div>
      </section>

      {/* Search Filters */}
      <section className="py-8 bg-white shadow-lg">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <DistrictCombobox
              placeholder="Select or type district..."
              value={searchFilters.location}
              onValueChange={(value) => setSearchFilters({ ...searchFilters, location: value })}
              className="border-gold-primary/30 focus:border-gold-primary"
            />

            <Input
              placeholder="Min Price (NPR)"
              type="number"
              value={searchFilters.minPrice}
              onChange={(e) => setSearchFilters({ ...searchFilters, minPrice: e.target.value })}
              className="border-gold-primary/30 focus:border-gold-primary"
            />

            <Input
              placeholder="Max Price (NPR)"
              type="number"
              value={searchFilters.maxPrice}
              onChange={(e) => setSearchFilters({ ...searchFilters, maxPrice: e.target.value })}
              className="border-gold-primary/30 focus:border-gold-primary"
            />

            <Select value={searchFilters.propertyType} onValueChange={(value) => setSearchFilters({ ...searchFilters, propertyType: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Property Type" />
              </SelectTrigger>
              <SelectContent>
                {propertyTypes.map((type) => (
                  <SelectItem key={type.id || type.name} value={type.name.toLowerCase()}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={searchFilters.bedrooms} onValueChange={(value) => setSearchFilters({ ...searchFilters, bedrooms: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Bedrooms" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1+ Bedroom</SelectItem>
                <SelectItem value="2">2+ Bedrooms</SelectItem>
                <SelectItem value="3">3+ Bedrooms</SelectItem>
                <SelectItem value="4">4+ Bedrooms</SelectItem>
                <SelectItem value="5">5+ Bedrooms</SelectItem>
              </SelectContent>
            </Select>

            <Button className="w-full bg-gradient-to-r from-gold-primary to-gold-light hover:from-gold-dark hover:to-gold-primary text-white shadow-lg hover:shadow-xl transition-all duration-300">
              Search Properties
            </Button>
          </div>
        </div>
      </section>

      {/* Properties Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gold-primary mx-auto mb-4"></div>
              <p className="text-lg text-gray-medium">Loading properties...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4 bg-gradient-to-r from-navy-primary to-navy-light hover:from-navy-dark hover:to-navy-primary text-white shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <>
              {/* Featured Properties */}
              {featuredProperties.length > 0 && (
                <div className="mb-16">
                  <h2 className="text-3xl font-bold text-navy-dark mb-8 text-center">
                    Featured Properties
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    {featuredProperties.map((property) => (
                      <div key={property.id} className="relative">
                        <PropertyCard property={property} />
                        <div className="absolute top-4 left-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                          Featured
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* All Properties */}
              <div>
                <h2 className="text-3xl font-bold text-navy-dark mb-8 text-center">
                  {filteredProperties.length > 0 ? 'All Properties' : 'No Properties Found'}
                </h2>

                {filteredProperties.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    {filteredProperties.map((property) => (
                      <div key={property.id} className="relative">
                        <PropertyCard property={property} />
                        {property.featured && (
                          <div className="absolute top-4 left-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold z-10">
                            Featured
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-gray-medium text-lg">
                      No properties match your search criteria.
                    </p>
                    <Button
                      onClick={() =>
                        setSearchFilters({
                          location: '',
                          minPrice: '',
                          maxPrice: '',
                          propertyType: '',
                          bedrooms: '',
                        })
                      }
                      className="mt-4 bg-gradient-to-r from-gold-primary to-gold-light hover:from-gold-dark hover:to-gold-primary text-white shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      Clear Filters
                    </Button>
                  </div>
                )}
              </div>

              {/* Load More - Only show if there are properties */}
              {filteredProperties.length > 0 && (
                <div className="text-center mt-12">
                  <Button className="bg-gradient-to-r from-navy-primary to-navy-light hover:from-navy-dark hover:to-navy-primary text-white shadow-lg hover:shadow-xl transition-all duration-300" size="lg">
                    Load More Properties
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </section>
    </div>
  );
};