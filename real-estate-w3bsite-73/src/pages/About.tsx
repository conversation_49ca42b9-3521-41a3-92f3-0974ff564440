import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { getReq } from '@/apiService';

interface AboutData {
  id: number;
  title: string;
  content: string;
  vision: string;
  mission: string;
  image1: string | null;
  image2: string | null;
  is_active: boolean;
}



export const About = () => {
  useScrollAnimation();

  const [about, setAbout] = useState<AboutData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);

    // Fetch about data
    getReq('about-us/')
      .then((data) => {
        if (data && typeof data === 'object') {
          // Handle both single object and array responses
          if (Array.isArray(data) && data.length > 0) {
            setAbout(data[0]);
          } else if (!Array.isArray(data) && data.id) {
            // Single object response
            setAbout(data);
          } else {
            setError('No about information found.');
            setAbout(null);
          }
        } else if (data === null) {
          setError('Failed to load data. API returned null.');
        } else {
          setError('No about information found.');
          setAbout(null);
        }
      })
      .catch((err) => {
        console.error('About.tsx: Error fetching data:', err);
        setError('Failed to load about information.');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-navy-primary to-navy-light text-white py-20">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            {about?.title || "About Simthali Real Esatate"} 
          </h1>
          
        </div>
      </section>

      {/* Company Story */}
      <section className="py-20">
        <div className="container mx-auto px-4 lg:px-8">
          {error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">{error}</p>
            </div>
          ) : loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-navy-primary mx-auto mb-4"></div>
              <p className="text-lg text-gray-medium">Loading about information...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="animate-slide-in-left">
                <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-6">
                  Our Story
                </h2>
                <div className="text-lg text-gray-medium mb-6">
                  {about?.content ?
                    about.content.split(/\r?\n/).map((paragraph, index) => (
                      <p key={index} className={index > 0 ? "mt-4" : ""}>
                        {paragraph}
                      </p>
                    )) :
                    <p>Founded in 2008 with a vision to transform the luxury real estate experience, Premier Properties has grown from a boutique Beverly Hills agency to a nationally recognized leader in premium property sales.</p>
                  }
                </div>
                {about?.vision && (
                  <div className="text-lg text-gray-medium mb-6">
                    <strong>Vision:</strong>{" "}
                    {about.vision.split(/\r?\n/).map((paragraph, index) => (
                      <span key={index}>
                        {index > 0 && <br />}
                        {paragraph}
                      </span>
                    ))}
                  </div>
                )}
                {about?.mission && (
                  <div className="text-lg text-gray-medium mb-8">
                    <strong>Mission:</strong>{" "}
                    {about.mission.split(/\r?\n/).map((paragraph, index) => (
                      <span key={index}>
                        {index > 0 && <br />}
                        {paragraph}
                      </span>
                    ))}
                  </div>
                )}
                <Button variant="navy" size="lg">
                  Learn More About Our Process
                </Button>
              </div>
              <div className="animate-fade-in">
                <img 
                  src={about?.image1 || "https://images.unsplash.com/photo-1486718448742-163732cd1544?w=800"} 
                  alt="About us"
                  className="w-full h-96 object-cover rounded-lg shadow-xl"
                  onError={(e) => {
                    e.currentTarget.src = "https://images.unsplash.com/photo-1486718448742-163732cd1544?w=800";
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </section>


    </div>
  );
};
