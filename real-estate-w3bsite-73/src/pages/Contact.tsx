import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { getReq, postReq } from '@/apiService';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthPrompt } from '@/hooks/useAuthPrompt';
import { AuthPrompt } from '@/components/auth/AuthPrompt';
import { Link } from 'react-router-dom';

export const Contact = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const {
    isPromptOpen,
    promptConfig,
    requireAuth,
    closePrompt,
    handleLogin,
    handleRegister
  } = useAuthPrompt();

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    preferred_contact: ''
  });

  const [organization, setOrganization] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    // Fetch organization data for contact information
    getReq('organization/')
      .then((data) => {
        if (data && typeof data === 'object') {
          setOrganization(data);
        } else {
          console.warn('Contact.tsx: No organization data found');
        }
      })
      .catch((err) => {
        console.error('Contact.tsx: Error fetching organization data:', err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // Prefill form data if user is logged in
  useEffect(() => {
    if (user) {
      const nameParts = user.name ? user.name.split(' ') : ['', ''];
      setFormData(prev => ({
        ...prev,
        first_name: nameParts[0] || '',
        last_name: nameParts.slice(1).join(' ') || '',
        email: user.email || '',
        phone: user.phone || ''
      }));
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is authenticated, if not, prompt for login
    if (!user) {
      requireAuth({
        feature: 'contact',
        title: 'Login Required',
        description: 'Please login or register to send us a message.'
      });
      return;
    }

    setSubmitting(true);

    try {
      const response = await postReq('contact/', formData);

      if (response) {
        toast({
          title: "Message Sent!",
          description: "Thank you for your message. We'll get back to you soon.",
        });

        // Reset form but keep user info prefilled
        setFormData(prev => ({
          ...prev,
          subject: '',
          message: '',
          preferred_contact: ''
        }));
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-navy-primary to-navy-light text-white py-20">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Contact Us
          </h1>
          <p className="text-xl text-gray-light max-w-2xl mx-auto">
            Ready to start your real estate journey? We're here to help you every step of the way
          </p>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <div className="animate-slide-in-left">
              <h2 className="text-4xl font-bold text-navy-dark mb-8">Get In Touch</h2>
              <Card className="p-8 shadow-xl">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-dark mb-2">First Name</label>
                      <Input
                        value={formData.first_name}
                        onChange={(e) => handleInputChange('first_name', e.target.value)}
                        placeholder="Enter your first name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-dark mb-2">Last Name</label>
                      <Input
                        value={formData.last_name}
                        onChange={(e) => handleInputChange('last_name', e.target.value)}
                        placeholder="Enter your last name"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-dark mb-2">Email Address</label>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Enter your email"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-dark mb-2">Phone Number</label>
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="Enter your phone number"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-dark mb-2">Subject</label>
                    <Select value={formData.subject} onValueChange={(value) => handleInputChange('subject', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a subject" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="buying">I'm interested in buying</SelectItem>
                        <SelectItem value="selling">I want to sell my property</SelectItem>
                        <SelectItem value="investment">Investment opportunities</SelectItem>
                        <SelectItem value="consultation">Schedule a consultation</SelectItem>
                        <SelectItem value="other">Other inquiry</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-dark mb-2">Preferred Contact Method</label>
                    <Select value={formData.preferred_contact} onValueChange={(value) => handleInputChange('preferred_contact', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="How would you like us to contact you?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="phone">Phone</SelectItem>
                        <SelectItem value="text">Text Message</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-dark mb-2">Message</label>
                    <Textarea
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      placeholder="Tell us about your real estate needs..."
                      rows={4}
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    variant="premium"
                    size="lg"
                    className="w-full"
                    disabled={submitting}
                  >
                    {submitting ? 'Sending...' : 'Send Message'}
                  </Button>
                </form>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="animate-fade-in">
              <h2 className="text-4xl font-bold text-navy-dark mb-8">Contact Information</h2>
              
              <div className="space-y-8">
                <Card className="p-6">
                  <h3 className="text-xl font-bold text-navy-dark mb-4">📞 Phone</h3>
                  <p className="text-lg text-gray-medium mb-2">
                    Main Office: {organization?.phone || '(*************'}
                  </p>
                  <p className="text-gray-medium">Available 24/7 for urgent matters</p>
                </Card>

                <Card className="p-6">
                  <h3 className="text-xl font-bold text-navy-dark mb-4">✉️ Email</h3>
                  <p className="text-lg text-gray-medium mb-2">
                    {organization?.email || '<EMAIL>'}
                  </p>
                  <p className="text-gray-medium">We respond within 2 hours during business hours</p>
                </Card>

                <Card className="p-6">
                  <h3 className="text-xl font-bold text-navy-dark mb-4">🏢 Headquarters</h3>
                  <p className="text-lg text-gray-medium mb-2">
                    {organization?.name || 'Premier Properties'}
                  </p>
                  <p className="text-lg text-gray-medium mb-2">
                    {organization?.address || '123 Luxury Avenue, Beverly Hills, CA 90210'}
                  </p>
                  <p className="text-gray-medium">Mon-Fri: 9AM-7PM, Sat-Sun: 10AM-5PM</p>
                </Card>

                <Card className="p-6">
                  <h3 className="text-xl font-bold text-navy-dark mb-4">🌐 Social Media</h3>
                  <div className="flex space-x-4">
                    {organization?.facebook && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={organization.facebook} target="_blank" rel="noopener noreferrer">Facebook</a>
                      </Button>
                    )}
                    {organization?.instagram && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={organization.instagram} target="_blank" rel="noopener noreferrer">Instagram</a>
                      </Button>
                    )}
                    {organization?.linkedin && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={organization.linkedin} target="_blank" rel="noopener noreferrer">LinkedIn</a>
                      </Button>
                    )}
                    {organization?.twitter && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={organization.twitter} target="_blank" rel="noopener noreferrer">Twitter</a>
                      </Button>
                    )}
                    {(!organization?.facebook && !organization?.instagram && !organization?.linkedin && !organization?.twitter) && (
                      <>
                        <Button variant="outline" size="sm">Facebook</Button>
                        <Button variant="outline" size="sm">Instagram</Button>
                        <Button variant="outline" size="sm">LinkedIn</Button>
                      </>
                    )}
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

    

      {/* Auth Prompt */}
      <AuthPrompt
        isOpen={isPromptOpen}
        onClose={closePrompt}
        feature={promptConfig.feature}
        title={promptConfig.title}
        description={promptConfig.description}
        onLogin={handleLogin}
        onRegister={handleRegister}
      />
    </div>
  );
};