import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { getReq } from '@/apiService';
import { X, ChevronLeft, ChevronRight, Eye, Image as ImageIcon, Calendar, ArrowRight, Grid, List } from 'lucide-react';

interface GalleryImage {
  id: number;
  image: string;
  title: string;
  description: string;
}

interface Gallery {
  id: number;
  title: string;
  description: string;
  images: GalleryImage[];
}

export const Gallery = () => {
  const [galleries, setGalleries] = useState<Gallery[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [allImages, setAllImages] = useState<GalleryImage[]>([]);
  const [selectedGallery, setSelectedGallery] = useState<Gallery | null>(null);
  const [isGalleryDetailOpen, setIsGalleryDetailOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    setLoading(true);
    getReq('gallery/')
      .then((data) => {
        if (Array.isArray(data)) {
          setGalleries(data);
          // Flatten all images for lightbox navigation
          const flatImages = data.flatMap((gallery: Gallery) => gallery.images || []);
          setAllImages(flatImages);
        } else if (data === null) {
          console.error('Gallery.tsx: API call returned null');
          setError('Failed to load gallery. API returned null.');
          setGalleries([]);
        } else {
          console.warn('Gallery.tsx: Data is not an array:', data);
          setError('No galleries found.');
          setGalleries([]);
        }
      })
      .catch((err) => {
        console.error('Gallery.tsx: Error fetching galleries:', err);
        setError('Failed to load galleries.');
        setGalleries([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const openLightbox = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    const index = allImages.findIndex(img => img.image === imageUrl);
    setCurrentImageIndex(index >= 0 ? index : 0);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
  };

  const nextImage = () => {
    if (allImages.length > 0) {
      const nextIndex = (currentImageIndex + 1) % allImages.length;
      setCurrentImageIndex(nextIndex);
      setSelectedImage(allImages[nextIndex].image);
    }
  };

  const prevImage = () => {
    if (allImages.length > 0) {
      const prevIndex = (currentImageIndex - 1 + allImages.length) % allImages.length;
      setCurrentImageIndex(prevIndex);
      setSelectedImage(allImages[prevIndex].image);
    }
  };

  const openGalleryDetail = (gallery: Gallery) => {
    setSelectedGallery(gallery);
    setIsGalleryDetailOpen(true);
  };

  const closeGalleryDetail = () => {
    setSelectedGallery(null);
    setIsGalleryDetailOpen(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-light flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gold-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-navy-primary via-navy-dark to-navy-light text-white py-24 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 opacity-30">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }}></div>
        </div>

        <div className="container mx-auto px-4 lg:px-8 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-bold mb-8 bg-gradient-to-r from-white via-gold-light to-white bg-clip-text text-transparent">
              Property Gallery
            </h1>
            <p className="text-xl md:text-2xl text-gray-light max-w-3xl mx-auto leading-relaxed mb-8">
              Discover our curated collection of exceptional properties through stunning visual narratives
            </p>
            <div className="flex items-center justify-center gap-4 text-sm text-gray-light">
              <div className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-gold-light" />
                <span>{galleries.reduce((total, gallery) => total + (gallery.images?.length || 0), 0)} Images</span>
              </div>
              <div className="w-1 h-1 bg-gold-primary rounded-full"></div>
              <div className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-gold-light" />
                <span>{galleries.length} Collections</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Content */}
      <section className="py-20 bg-gray-light">
        <div className="container mx-auto px-4 lg:px-8">
          {error ? (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto">
                <ImageIcon className="h-16 w-16 mx-auto text-destructive mb-4" />
                <h3 className="text-xl font-semibold text-navy-dark mb-2">Error Loading Gallery</h3>
                <p className="text-destructive">{error}</p>
              </div>
            </div>
          ) : galleries.length === 0 ? (
            <div className="text-center py-20">
              <div className="max-w-md mx-auto">
                <ImageIcon className="h-20 w-20 mx-auto text-gold-primary mb-6" />
                <h3 className="text-2xl font-semibold text-navy-dark mb-4">No Galleries Available</h3>
                <p className="text-gray-medium text-lg">Check back soon for our latest property showcases.</p>
              </div>
            </div>
          ) : (
            <div className="space-y-12">
              {/* View Controls */}
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold text-navy-dark mb-2">Our Collections</h2>
                  <p className="text-gray-medium">Explore {galleries.length} curated property galleries</p>
                </div>
                <div className="flex items-center gap-2 bg-white rounded-lg p-1 shadow-sm">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="h-8"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="h-8"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Galleries Grid/List */}
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {galleries.map((gallery) => (
                    <Card key={gallery.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group bg-white">
                      <div className="relative aspect-[4/3] overflow-hidden">
                        {gallery.images && gallery.images.length > 0 ? (
                          <img
                            src={gallery.images[0].image}
                            alt={gallery.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=600";
                            }}
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                            <ImageIcon className="h-12 w-12 text-gray-400" />
                          </div>
                        )}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        <div className="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Badge className="bg-gold-primary/80 text-white border-gold-primary/50 mb-2">
                            {gallery.images?.length || 0} Images
                          </Badge>
                        </div>
                      </div>

                      <CardContent className="p-6">
                        <h3 className="text-xl font-bold text-navy-dark mb-2 group-hover:text-gold-primary transition-colors">
                          {gallery.title}
                        </h3>
                        {gallery.description && (
                          <p className="text-gray-medium mb-4 line-clamp-2">
                            {gallery.description}
                          </p>
                        )}
                        <Button
                          variant="outline"
                          className="w-full border-gold-primary text-gold-primary group-hover:bg-gold-primary group-hover:text-white transition-all duration-300"
                          onClick={() => openGalleryDetail(gallery)}
                        >
                          View Gallery
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="space-y-6">
                  {galleries.map((gallery) => (
                    <Card key={gallery.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 bg-white">
                      <div className="flex flex-col md:flex-row">
                        <div className="md:w-1/3 aspect-[4/3] md:aspect-auto overflow-hidden">
                          {gallery.images && gallery.images.length > 0 ? (
                            <img
                              src={gallery.images[0].image}
                              alt={gallery.title}
                              className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400";
                              }}
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                              <ImageIcon className="h-12 w-12 text-gray-400" />
                            </div>
                          )}
                        </div>

                        <CardContent className="md:w-2/3 p-6 flex flex-col justify-between">
                          <div>
                            <div className="flex items-start justify-between mb-3">
                              <h3 className="text-2xl font-bold text-navy-dark">{gallery.title}</h3>
                              <Badge variant="outline" className="ml-4">
                                {gallery.images?.length || 0} Images
                              </Badge>
                            </div>
                            {gallery.description && (
                              <p className="text-gray-600 mb-4 leading-relaxed">
                                {gallery.description}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-sm text-gray-medium">
                              <Calendar className="h-4 w-4 text-gold-primary" />
                              <span>Updated recently</span>
                            </div>
                            <Button
                              variant="outline"
                              onClick={() => openGalleryDetail(gallery)}
                              className="border-gold-primary text-gold-primary hover:bg-gold-primary hover:text-white transition-all duration-300"
                            >
                              Explore Gallery
                              <ArrowRight className="h-4 w-4 ml-2" />
                            </Button>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Lightbox */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedImage}
              alt="Gallery image"
              className="max-w-full max-h-full object-contain"
            />
            
            {/* Close button */}
            <Button
              variant="outline"
              size="sm"
              className="absolute top-4 right-4 bg-white text-black hover:bg-gray-100"
              onClick={closeLightbox}
            >
              <X size={20} />
            </Button>

            {/* Navigation buttons */}
            {allImages.length > 1 && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white text-black hover:bg-gray-100"
                  onClick={prevImage}
                >
                  <ChevronLeft size={20} />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white text-black hover:bg-gray-100"
                  onClick={nextImage}
                >
                  <ChevronRight size={20} />
                </Button>
              </>
            )}

            {/* Image counter */}
            {allImages.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
                {currentImageIndex + 1} / {allImages.length}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Gallery Detail Dialog */}
      <Dialog open={isGalleryDetailOpen} onOpenChange={setIsGalleryDetailOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-navy-dark">
              {selectedGallery?.title}
            </DialogTitle>
            <DialogDescription className="text-lg">
              {selectedGallery?.description}
            </DialogDescription>
          </DialogHeader>

          {selectedGallery && (
            <div className="space-y-6">
              {/* Gallery Stats */}
              <div className="flex items-center gap-6 p-4 bg-gray-light rounded-lg border border-gold-primary/20">
                <div className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5 text-gold-primary" />
                  <span className="font-medium text-navy-dark">{selectedGallery.images?.length || 0} Images</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-gold-primary" />
                  <span className="text-gray-medium">Updated recently</span>
                </div>
              </div>

              {/* Images Grid */}
              {selectedGallery.images && selectedGallery.images.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {selectedGallery.images.map((image) => (
                    <Card
                      key={image.id}
                      className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
                      onClick={() => openLightbox(image.image)}
                    >
                      <div className="relative aspect-square overflow-hidden">
                        <img
                          src={image.image}
                          alt={image.title || selectedGallery.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400";
                          }}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                          <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <Eye className="h-6 w-6" />
                          </div>
                        </div>
                      </div>
                      {image.title && (
                        <CardContent className="p-3">
                          <h4 className="font-medium text-sm text-navy-dark truncate">{image.title}</h4>
                          {image.description && (
                            <p className="text-xs text-gray-medium mt-1 line-clamp-2">{image.description}</p>
                          )}
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <ImageIcon className="h-16 w-16 mx-auto text-gold-primary mb-4" />
                  <h3 className="text-lg font-medium text-navy-dark mb-2">No Images</h3>
                  <p className="text-gray-medium">This gallery doesn't have any images yet.</p>
                </div>
              )}

              <div className="flex justify-end pt-4">
                <Button
                  variant="outline"
                  onClick={closeGalleryDetail}
                  className="border-gold-primary text-gold-primary hover:bg-gold-primary hover:text-white transition-all duration-300"
                >
                  Close Gallery
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
