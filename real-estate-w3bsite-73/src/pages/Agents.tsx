import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getReq } from '@/apiService';
import { Team } from '@/types';
import { Mail, Phone, Linkedin, Twitter, Facebook, Instagram } from 'lucide-react';

interface Agent {
  id: number;
  name: string;
  full_name: string;
  first_name: string;
  last_name: string;
  title: string;
  specialty: string;
  email: string;
  phone: string;
  experience: string;
  sales: string;
  photo: string | null;
  image: string | null;
  bio: string;
  specialization_names: string;
  specialties: string[];
  experience_years: number;
  license_number: string;
  whatsapp: string;
  linkedin: string;
  facebook: string;
  instagram: string;
  profile_picture: string | null;
  is_active: boolean;
}

export const Agents = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [team, setTeam] = useState<Team[]>([]);
  const [teamLoading, setTeamLoading] = useState(false);
  const [teamError, setTeamError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    getReq('agents/')
      .then((data) => {
        // Handle both paginated and non-paginated responses
        const agentsArray = Array.isArray(data) ? data : (data?.results || []);

        if (agentsArray.length > 0) {
          // Use the new API structure directly
          setAgents(agentsArray);
        } else if (data === null) {
          console.error('Agents.tsx: API call returned null');
          setError('Failed to load agents. API returned null.');
          setAgents([]);
        } else {
          console.warn('Agents.tsx: Data is not an array:', data);
          setError('No agents found.');
          setAgents([]);
        }
      })
      .catch((err) => {
        console.error('Agents.tsx: Error fetching agents:', err);
        setError('Failed to load agents.');
        setAgents([]);
      })
      .finally(() => {
        setLoading(false);
      });

    // Fetch team data
    setTeamLoading(true);
    getReq('team/')
      .then((data) => {
        if (Array.isArray(data)) {
          setTeam(data);
        } else if (data === null) {
          console.error('Agents.tsx: Team API call returned null');
          setTeamError('Failed to load team data. API returned null.');
        } else {
          console.warn('Agents.tsx: Team data is not an array:', data);
          setTeamError('No team information found.');
          setTeam([]);
        }
      })
      .catch((err) => {
        console.error('Agents.tsx: Error fetching team data:', err);
        setTeamError('Failed to load team information.');
      })
      .finally(() => {
        setTeamLoading(false);
      });
  }, []);



  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-navy-primary to-navy-light text-white py-20">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Meet Our Expert Team
          </h1>
          <p className="text-xl text-gray-light max-w-2xl mx-auto">
            Our experienced agents are dedicated to providing exceptional service and results
          </p>
        </div>
      </section>

      {/* Team Stats */}
      <section className="py-16 bg-gray-light">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <h3 className="text-4xl font-bold text-navy-primary mb-2">50+</h3>
              <p className="text-gray-medium">Expert Agents</p>
            </div>
            <div>
              <h3 className="text-4xl font-bold text-navy-primary mb-2">95%</h3>
              <p className="text-gray-medium">Client Retention</p>
            </div>
            <div>
              <h3 className="text-4xl font-bold text-navy-primary mb-2">$2.5B</h3>
              <p className="text-gray-medium">Total Sales</p>
            </div>
            <div>
              <h3 className="text-4xl font-bold text-navy-primary mb-2">24/7</h3>
              <p className="text-gray-medium">Client Support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Agents Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Our Top Agents
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              Meet the professionals who will guide you through your real estate journey
            </p>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-navy-primary mx-auto mb-4"></div>
              <p className="text-lg text-gray-medium">Loading agents...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">{error}</p>
            </div>
          ) : agents.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-lg text-gray-medium">No agents currently available</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {agents.map((agent, index) => (
              <Card key={agent.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="relative">
                  <img
                    src={agent.photo || agent.image || "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400"}
                    alt={agent.full_name || agent.name}
                    className="w-full h-80 object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400";
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-navy-dark/80 via-transparent to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">{agent.full_name || agent.name}</h3>
                    <p className="text-sm text-gray-light">{agent.title || agent.specialty}</p>
                  </div>
                </div>
                
                <CardHeader>
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <div className="text-sm text-gray-medium">Experience</div>
                      <div className="font-semibold text-navy-primary">
                        {agent.experience_years ? `${agent.experience_years} years` : agent.experience || 'No Experience'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-medium">License</div>
                      <div className="font-semibold text-navy-primary">
                        {agent.license_number || agent.sales || 'Licensed'}
                      </div>
                    </div>
                  </div>

                  {(agent.specialization_names || agent.specialties) && (
                    <div className="mb-4">
                      <div className="text-sm text-gray-medium mb-2">Specialties</div>
                      <div className="flex flex-wrap gap-2">
                        {agent.specialization_names ? (
                          agent.specialization_names.split(', ').map((specialty: string, idx: number) => (
                            <span key={idx} className="bg-navy-primary/10 text-navy-primary px-2 py-1 rounded-full text-xs">
                              {specialty}
                            </span>
                          ))
                        ) : agent.specialties ? (
                          agent.specialties.map((specialty: string, idx: number) => (
                            <span key={idx} className="bg-navy-primary/10 text-navy-primary px-2 py-1 rounded-full text-xs">
                              {specialty}
                            </span>
                          ))
                        ) : (
                          <span className="bg-navy-primary/10 text-navy-primary px-2 py-1 rounded-full text-xs">
                            General Real Estate
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </CardHeader>
                
                <CardContent>
                  <p className="text-gray-medium text-sm mb-6">{agent.bio}</p>
                  
                  <div className="space-y-2 mb-6">
                    <div className="flex items-center text-sm">
                      <span className="text-gray-medium w-12">Phone:</span>
                      <span className="text-navy-primary">{agent.phone}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <span className="text-gray-medium w-12">Email:</span>
                      <span className="text-navy-primary text-xs">{agent.email}</span>
                    </div>
                    {agent.whatsapp && (
                      <div className="flex items-center text-sm">
                        <span className="text-gray-medium w-12">WhatsApp:</span>
                        <a href={`https://wa.me/${agent.whatsapp.replace(/[^0-9]/g, '')}`}
                           className="text-green-600 hover:underline text-xs"
                           target="_blank"
                           rel="noopener noreferrer">
                          {agent.whatsapp}
                        </a>
                      </div>
                    )}
                  </div>

                  {/* Social Media Links */}
                  {(agent.linkedin || agent.facebook || agent.instagram) && (
                    <div className="flex gap-2 mb-4">
                      {agent.linkedin && (
                        <a href={agent.linkedin} target="_blank" rel="noopener noreferrer"
                           className="text-blue-600 hover:text-blue-800">
                          <span className="text-xs">LinkedIn</span>
                        </a>
                      )}
                      {agent.facebook && (
                        <a href={agent.facebook} target="_blank" rel="noopener noreferrer"
                           className="text-blue-600 hover:text-blue-800">
                          <span className="text-xs">Facebook</span>
                        </a>
                      )}
                      {agent.instagram && (
                        <a href={agent.instagram} target="_blank" rel="noopener noreferrer"
                           className="text-pink-600 hover:text-pink-800">
                          <span className="text-xs">Instagram</span>
                        </a>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">Our Team</h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              Meet the dedicated professionals who make our success possible
            </p>
          </div>

          {teamLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary mx-auto mb-4"></div>
              <p className="text-gray-medium">Loading team information...</p>
            </div>
          ) : teamError ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">{teamError}</p>
            </div>
          ) : team.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {team.map((member) => (
                <Card key={member.id} className="text-center hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="w-32 h-32 rounded-full mx-auto mb-4 bg-gray-200 flex items-center justify-center overflow-hidden">
                      {member.image ? (
                        <img
                          src={member.image}
                          alt={member.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            e.currentTarget.parentElement!.innerHTML = `
                              <div class="w-full h-full bg-gradient-to-br from-navy-primary to-navy-dark flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">${member.name.split(' ').map(n => n[0]).join('')}</span>
                              </div>
                            `;
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-navy-primary to-navy-dark flex items-center justify-center">
                          <span className="text-white text-2xl font-bold">
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                      )}
                    </div>
                    <CardTitle className="text-xl font-bold text-navy-dark">{member.name}</CardTitle>
                    <CardDescription className="text-navy-primary font-medium">
                      {member.position}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {member.bio && (
                      <p className="text-gray-medium text-sm leading-relaxed mb-4">
                        {member.bio}
                      </p>
                    )}

                    {/* Contact Information */}
                    <div className="space-y-2 mb-4">
                      {member.email && (
                        <div className="flex items-center justify-center gap-2 text-sm text-navy-primary">
                          <Mail className="w-4 h-4" />
                          <span>{member.email}</span>
                        </div>
                      )}
                      {member.phone && (
                        <div className="flex items-center justify-center gap-2 text-sm text-navy-primary">
                          <Phone className="w-4 h-4" />
                          <span>{member.phone}</span>
                        </div>
                      )}
                    </div>

                    {/* Social Media Links */}
                    <div className="flex justify-center gap-3">
                      {member.linkedin && (
                        <a
                          href={member.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Linkedin className="w-5 h-5" />
                        </a>
                      )}
                      {member.twitter && (
                        <a
                          href={member.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-blue-400 transition-colors"
                        >
                          <Twitter className="w-5 h-5" />
                        </a>
                      )}
                      {member.facebook && (
                        <a
                          href={member.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-blue-700 transition-colors"
                        >
                          <Facebook className="w-5 h-5" />
                        </a>
                      )}
                      {member.instagram && (
                        <a
                          href={member.instagram}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-400 hover:text-pink-600 transition-colors"
                        >
                          <Instagram className="w-5 h-5" />
                        </a>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-medium text-lg">No team information available.</p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-navy-primary to-navy-light text-white">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Work with Our Team?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto text-gray-light">
            Connect with one of our expert agents today and start your real estate journey
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button variant="premium" size="lg" className="text-lg px-8 py-4">
              Schedule Consultation
            </Button>
            <Button variant="hero" size="lg" className="text-lg px-8 py-4">
              Contact Our Team
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};