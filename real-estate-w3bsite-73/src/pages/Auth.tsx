import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Link } from 'react-router-dom';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { ArrowLeft, Building2, Users, Shield, Star } from 'lucide-react';
import logo from '@/assets/logo.png';

// Schemas
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  phone: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type LoginFormData = z.infer<typeof loginSchema>;
type RegisterFormData = z.infer<typeof registerSchema>;

export const Auth = () => {
  const [searchParams] = useSearchParams();
  const defaultTab = searchParams.get('tab') as 'login' | 'register' || 'login';
  const [activeTab, setActiveTab] = useState<'login' | 'register'>(defaultTab);
  const [isLoading, setIsLoading] = useState(false);
  const { login, register, isAuthenticated } = useAuth();
  const { organization } = useOrganization();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        if (userData.roles === 'admin') {
          navigate('/admin-dashboard');
        } else {
          navigate('/customer-dashboard');
        }
      } else {
        navigate('/');
      }
    }
  }, [isAuthenticated, navigate]);

  const loginForm = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const registerForm = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
    },
  });

  const onLoginSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      const success = await login(data.username, data.password);
      
      if (success) {
        toast({
          title: 'Welcome back!',
          description: 'You have successfully logged in.',
          duration: 3000,
        });
        loginForm.reset();

        // Redirect based on role
        setTimeout(() => {
          const storedUser = localStorage.getItem('user');
          if (storedUser) {
            const userData = JSON.parse(storedUser);
            if (userData.roles === 'admin') {
              navigate('/admin-dashboard');
            } else {
              navigate('/customer-dashboard');
            }
          } else {
            navigate('/');
          }
        }, 100);
      } else {
        throw new Error('Login failed: Invalid response from server');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      
      let errorMessage = 'An unexpected error occurred during login.';
      
      if (error.message.includes('Failed to get CSRF token')) {
        errorMessage = 'Unable to establish a secure connection. Please try again.';
      } else if (error.message.includes('Invalid credentials')) {
        errorMessage = 'Invalid username or password. Please try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: 'Login failed',
        description: errorMessage,
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onRegisterSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    try {
      const success = await register({
        name: data.name,
        email: data.email,
        roles: 'customer',
        phone: data.phone,
        password: data.password,
      });

      if (success) {
        toast({
          title: 'Account created!',
          description: 'Welcome to Premier Properties. You are now logged in.',
        });
        registerForm.reset();
        navigate('/customer-dashboard');
      } else {
        toast({
          title: 'Registration failed',
          description: 'Please check your information and try again. Make sure your email is unique and password meets requirements.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast({
        title: 'Registration failed',
        description: 'An error occurred while creating your account. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const switchTab = (tab: 'login' | 'register') => {
    setActiveTab(tab);
    loginForm.reset();
    registerForm.reset();
  };

  const features = [
    {
      icon: Building2,
      title: "Premium Properties",
      description: "Access to exclusive luxury properties and investment opportunities"
    },
    {
      icon: Users,
      title: "Expert Agents",
      description: "Work with our experienced real estate professionals"
    },
    {
      icon: Shield,
      title: "Secure Transactions",
      description: "Safe and secure property transactions with full legal support"
    },
    {
      icon: Star,
      title: "Personalized Service",
      description: "Tailored property recommendations based on your preferences"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy-primary via-navy-dark to-gray-900 flex">
      {/* Left Side - Branding & Features */}
      <div className="hidden lg:flex lg:w-1/2 flex-col justify-center px-12 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full"></div>
          <div className="absolute top-40 right-32 w-24 h-24 border border-gold-primary/30 rounded-full"></div>
          <div className="absolute bottom-32 left-32 w-40 h-40 border border-white/10 rounded-full"></div>
        </div>

        <div className="relative z-10">
          {/* Logo & Company Name */}
          <div className="mb-12">
            <div className="flex items-center mb-6">
              <img 
                src={organization?.logo || logo} 
                alt={organization?.name || "Premier Properties"} 
                className="h-12 w-12 mr-4"
              />
              <h1 className="text-3xl font-bold text-white">
                {organization?.name || "Premier Properties"}
              </h1>
            </div>
            <p className="text-xl text-white/80 leading-relaxed">
              {organization?.description || "Your trusted partner in finding the perfect property. Experience luxury, comfort, and exceptional service."}
            </p>
          </div>

          {/* Features */}
          <div className="space-y-6">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gold-primary/20 rounded-lg flex items-center justify-center">
                  <feature.icon className="w-6 h-6 text-gold-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">{feature.title}</h3>
                  <p className="text-white/70">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Side - Auth Form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center px-8 lg:px-12 bg-white relative">
        {/* Back to Home Button */}
        <div className="absolute top-6 left-6">
          <Link 
            to="/" 
            className="flex items-center text-gray-600 hover:text-navy-primary transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
        </div>

        {/* Mobile Logo */}
        <div className="lg:hidden text-center mb-8 mt-16">
          <img 
            src={organization?.logo || logo} 
            alt={organization?.name || "Premier Properties"} 
            className="h-16 w-16 mx-auto mb-4"
          />
          <h1 className="text-2xl font-bold text-navy-primary">
            {organization?.name || "Premier Properties"}
          </h1>
        </div>

        <div className="max-w-md mx-auto w-full">
          {/* Tab Header */}
          <div className="mb-8">
            <div className="flex bg-gray-100 rounded-lg p-1 mb-6">
              <button
                onClick={() => switchTab('login')}
                className={cn(
                  "flex-1 py-3 px-4 text-center font-semibold rounded-md transition-all duration-300",
                  activeTab === 'login' 
                    ? "bg-white text-navy-primary shadow-sm" 
                    : "text-gray-600 hover:text-navy-primary"
                )}
              >
                Sign In
              </button>
              <button
                onClick={() => switchTab('register')}
                className={cn(
                  "flex-1 py-3 px-4 text-center font-semibold rounded-md transition-all duration-300",
                  activeTab === 'register' 
                    ? "bg-white text-navy-primary shadow-sm" 
                    : "text-gray-600 hover:text-navy-primary"
                )}
              >
                Sign Up
              </button>
            </div>

            <div className="text-center">
              <h2 className="text-3xl font-bold text-navy-primary mb-2">
                {activeTab === 'login' ? 'Welcome Back' : 'Get Started'}
              </h2>
              <p className="text-gray-600">
                {activeTab === 'login' 
                  ? 'Sign in to your account to access your dashboard' 
                  : 'Create your account to start your real estate journey'
                }
              </p>
            </div>
          </div>

          {/* Forms Container */}
          <div className="relative overflow-hidden">
            {/* Login Form */}
            <div className={cn(
              "transition-all duration-500 ease-in-out",
              activeTab === 'login' 
                ? "translate-x-0 opacity-100" 
                : "-translate-x-full opacity-0 absolute inset-0"
            )}>
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-6">
                  <FormField
                    control={loginForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-navy-dark">Username</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your username" 
                            className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-navy-dark">Password</FormLabel>
                        <FormControl>
                          <Input 
                            type="password" 
                            placeholder="Enter your password" 
                            className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full h-12 text-lg"
                    variant="premium"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Signing in...' : 'Sign In'}
                  </Button>
                </form>
              </Form>
            </div>

            {/* Register Form */}
            <div className={cn(
              "transition-all duration-500 ease-in-out",
              activeTab === 'register' 
                ? "translate-x-0 opacity-100" 
                : "translate-x-full opacity-0 absolute inset-0"
            )}>
              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-6">
                  <FormField
                    control={registerForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-navy-dark">Full Name</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your full name" 
                            className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-navy-dark">Email</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your email" 
                            className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-navy-dark">Phone (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter your phone number" 
                            className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={registerForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-navy-dark">Password</FormLabel>
                          <FormControl>
                            <Input 
                              type="password" 
                              placeholder="Create password" 
                              className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={registerForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-navy-dark">Confirm Password</FormLabel>
                          <FormControl>
                            <Input 
                              type="password" 
                              placeholder="Confirm password" 
                              className="h-12 border-gray-300 focus:border-gold-primary focus:ring-gold-primary"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 text-lg"
                    variant="premium"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Creating account...' : 'Create Account'}
                  </Button>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
