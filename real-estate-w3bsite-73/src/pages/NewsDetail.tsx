import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getReq } from '@/apiService';
import { Calendar, ArrowLeft, Share2 } from 'lucide-react';

interface NewsCategory {
  id: number;
  name: string;
  description: string;
}

interface NewsArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image: string | null;
  category_details: NewsCategory | null;
  is_featured: boolean;
  published_at: string;
  meta_title?: string;
  meta_description?: string;
}

export const NewsDetail = () => {
  const { slug } = useParams<{ slug: string }>();
  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [relatedNews, setRelatedNews] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (slug) {
      setLoading(true);
      getReq(`news/${slug}/`)
        .then((data) => {
          if (data && typeof data === 'object') {
            setArticle(data);
            
            // Fetch related news from the same category
            if (data.category_details) {
              getReq(`news/?category=${data.category_details.id}`)
                .then((relatedData) => {
                  if (Array.isArray(relatedData)) {
                    // Filter out current article and limit to 3
                    const filtered = relatedData
                      .filter((item: NewsArticle) => item.slug !== slug)
                      .slice(0, 3);
                    setRelatedNews(filtered);
                  }
                })
                .catch((err) => {
                  console.error('NewsDetail.tsx: Error fetching related news:', err);
                });
            }
          } else {
            setError('Article not found.');
          }
        })
        .catch((err) => {
          console.error('NewsDetail.tsx: Error fetching article:', err);
          setError('Failed to load article.');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [slug]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: article?.title,
        text: article?.excerpt,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-light flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-navy-dark mb-4">
            {error || 'Article not found'}
          </h2>
          <Link to="/news">
            <Button>
              <ArrowLeft size={16} className="mr-2" />
              Back to News
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 lg:px-8 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-navy-primary hover:underline">Home</Link>
            <span className="text-gray-medium">/</span>
            <Link to="/news" className="text-navy-primary hover:underline">News</Link>
            <span className="text-gray-medium">/</span>
            <span className="text-gray-dark">{article.title}</span>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <article className="py-12">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center gap-2 text-sm text-gray-medium mb-4">
                <Calendar size={16} />
                <span>{formatDate(article.published_at)}</span>
                {article.category_details && (
                  <>
                    <span>•</span>
                    <Badge variant="secondary">{article.category_details.name}</Badge>
                  </>
                )}
                {article.is_featured && (
                  <>
                    <span>•</span>
                    <Badge className="bg-gold-primary text-white">Featured</Badge>
                  </>
                )}
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
                {article.title}
              </h1>
              
              {article.excerpt && (
                <p className="text-xl text-gray-medium leading-relaxed">
                  {article.excerpt}
                </p>
              )}

              <div className="flex items-center justify-between mt-6">
                <Link to="/news">
                  <Button variant="outline">
                    <ArrowLeft size={16} className="mr-2" />
                    Back to News
                  </Button>
                </Link>
                <Button variant="outline" onClick={handleShare}>
                  <Share2 size={16} className="mr-2" />
                  Share
                </Button>
              </div>
            </header>

            {/* Featured Image */}
            {article.featured_image && (
              <div className="mb-8">
                <img
                  src={article.featured_image}
                  alt={article.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=800";
                  }}
                />
              </div>
            )}

            {/* Article Body */}
            <div className="prose prose-lg max-w-none">
              <div 
                className="text-gray-dark leading-relaxed"
                dangerouslySetInnerHTML={{ __html: article.content.replace(/\n/g, '<br />') }}
              />
            </div>
          </div>
        </div>
      </article>

      {/* Related News */}
      {relatedNews.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-navy-dark mb-8 text-center">
                Related Articles
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedNews.map((relatedArticle) => (
                  <Card key={relatedArticle.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    {relatedArticle.featured_image && (
                      <div className="aspect-video overflow-hidden">
                        <img
                          src={relatedArticle.featured_image}
                          alt={relatedArticle.title}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400";
                          }}
                        />
                      </div>
                    )}
                    <CardContent className="p-4">
                      <div className="text-sm text-gray-medium mb-2">
                        {formatDate(relatedArticle.published_at)}
                      </div>
                      <h3 className="font-semibold text-navy-dark mb-2 line-clamp-2">
                        {relatedArticle.title}
                      </h3>
                      <Link to={`/news/${relatedArticle.slug}`}>
                        <Button variant="outline" size="sm">
                          Read More
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </div>
  );
};
