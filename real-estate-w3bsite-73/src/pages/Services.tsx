import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { getReq } from '@/apiService';

interface Service {
  title: string;
  description: string;
  features?: string[];
  icon: string;
  image?: string;
}

export const Services = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    getReq('services/')
      .then((data) => {
        if (Array.isArray(data)) {
          setServices(data);
        } else if (data === null) {
          setError('Failed to load services. API returned null.');
          setServices([]);
        } else {
          setError('No services found.');
          setServices([]);
        }
      })
      .catch((err) => {
        setError('Failed to load services.');
        setServices([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  
  const additionalServices = [
    {
      title: "Market Analysis",
      description: "Comprehensive market research and property valuation services",
      icon: "📊"
    },
    {
      title: "Relocation Services",
      description: "Complete assistance for clients moving to new cities or countries",
      icon: "✈️"
    },
    {
      title: "New Construction",
      description: "Exclusive access to pre-construction and new development projects",
      icon: "🏗️"
    },
    {
      title: "Commercial Real Estate",
      description: "Expert guidance for commercial property investments and sales",
      icon: "🏢"
    },
    {
      title: "Estate Planning",
      description: "Real estate strategies for estate planning and wealth transfer",
      icon: "📋"
    },
    {
      title: "International Sales",
      description: "Global network for international property transactions",
      icon: "🌍"
    }
  ];

  const process = [
    {
      step: "01",
      title: "Initial Consultation",
      description: "We begin with a comprehensive consultation to understand your unique needs, goals, and timeline."
    },
    {
      step: "02",
      title: "Market Analysis",
      description: "Our team conducts thorough market research to provide you with accurate pricing and opportunities."
    },
    {
      step: "03",
      title: "Strategic Planning",
      description: "We develop a customized strategy tailored to your specific objectives and market conditions."
    },
    {
      step: "04",
      title: "Implementation",
      description: "Our experts execute the plan with precision, keeping you informed throughout the process."
    },
    {
      step: "05",
      title: "Closing & Beyond",
      description: "We ensure a smooth closing and provide ongoing support for your continued success."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-navy-primary to-navy-light text-white py-20">
        <div className="container mx-auto px-4 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            Our Services
          </h1>
          <p className="text-xl text-gray-light max-w-2xl mx-auto">
            Comprehensive real estate solutions tailored to your unique needs and goals
          </p>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Premium Services
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              Our core services designed to deliver exceptional results
            </p>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-navy-primary mx-auto mb-4"></div>
              <p className="text-lg text-gray-medium">Loading services...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 text-lg">{error}</p>
            </div>
          ) : services.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-lg text-gray-medium">No services currently available</p>
            </div>
          ) : (
            <div className="space-y-20">
              {services.map((service, index) => (
              <div key={service.title} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                <div className={`animate-slide-in-left ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                  <div className="flex items-center mb-6">
                    <div className="text-4xl mr-4">{service.icon}</div>
                    <h3 className="text-3xl font-bold text-navy-dark">{service.title}</h3>
                  </div>
                  <p className="text-lg text-gray-medium mb-8">{service.description}</p>
                  
                  {service.features && service.features.length > 0 && (
                    <div className="space-y-3 mb-8">
                      {service.features.map((feature: string, idx: number) => (
                        <div key={idx} className="flex items-center">
                          <div className="w-2 h-2 bg-gold-primary rounded-full mr-3"></div>
                          <span className="text-gray-dark">{feature}</span>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  <Button variant="navy" size="lg">
                    Learn More
                  </Button>
                </div>
                
                <div className={`animate-fade-in ${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                  <img 
                    src={service.image} 
                    alt={service.title}
                    className="w-full h-96 object-cover rounded-lg shadow-xl"
                  />
                </div>
              </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Additional Services */}
      <section className="py-20 bg-gray-light">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Additional Services
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              Specialized services to meet all your real estate needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalServices.map((service, index) => (
              <Card key={service.title} className="text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-2 animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                <CardHeader>
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <CardTitle className="text-xl text-navy-dark">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-medium mb-4">
                    {service.description}
                  </CardDescription>
                  <Button variant="elegant" size="sm">
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Our Process */}
      <section className="py-20">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Our Process
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              A proven methodology that ensures exceptional results every time
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {process.map((step, index) => (
                <div key={step.step} className="flex items-start space-x-6 animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-gradient-to-br from-gold-dark to-gold-primary rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {step.step}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-navy-dark mb-2">{step.title}</h3>
                    <p className="text-lg text-gray-medium">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-light">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-navy-dark mb-4">
              Why Choose Simthali Real Estate?
            </h2>
            <p className="text-xl text-gray-medium max-w-2xl mx-auto">
              What sets us apart in the luxury real estate market
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
            <div className="text-center animate-fade-in">
              <div className="w-20 h-20 bg-navy-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">⚡</span>
              </div>
              <h3 className="text-2xl font-bold text-navy-dark mb-4">Lightning Fast Response</h3>
              <p className="text-gray-medium">24/7 availability with response times under 1 hour during business hours</p>
            </div>

            <div className="text-center animate-fade-in" style={{ animationDelay: '0.1s' }}>
              <div className="w-20 h-20 bg-navy-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">🌟</span>
              </div>
              <h3 className="text-2xl font-bold text-navy-dark mb-4">Exceptional Results</h3>
              <p className="text-gray-medium">98% client satisfaction rate with properties selling 15% above market average</p>
            </div>

            <div className="text-center animate-fade-in" style={{ animationDelay: '0.2s' }}>
              <div className="w-20 h-20 bg-navy-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">🔒</span>
              </div>
              <h3 className="text-2xl font-bold text-navy-dark mb-4">Complete Confidentiality</h3>
              <p className="text-gray-medium">Discretion and privacy protection for all high-profile and celebrity clients</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      
    </div>
  );
};