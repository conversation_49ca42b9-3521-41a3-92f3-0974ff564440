/**
 * Utility functions for property-related operations
 */

import { Property } from '@/types';

/**
 * Format land area in Ropani-Aana-Paisa-Daam format
 */
export const formatLandArea = (
  ropani?: number,
  aana?: number,
  paisa?: number,
  daam?: number
): string => {
  const r = ropani || 0;
  const a = aana || 0;
  const p = paisa || 0;
  const d = daam || 0;
  return `${r}-${a}-${p}-${d}`;
};

/**
 * Parse land area from formatted string (e.g., "0-3-2-1")
 */
export const parseLandArea = (formatted: string): {
  ropani: number;
  aana: number;
  paisa: number;
  daam: number;
} => {
  const parts = formatted.split('-').map(p => parseInt(p) || 0);
  return {
    ropani: parts[0] || 0,
    aana: parts[1] || 0,
    paisa: parts[2] || 0,
    daam: parts[3] || 0,
  };
};

/**
 * Get human-readable land area display
 */
export const getLandAreaDisplay = (
  ropani?: number,
  aana?: number,
  paisa?: number,
  daam?: number
): string | null => {
  const parts: string[] = [];
  
  if (ropani && ropani > 0) {
    parts.push(`${ropani} Ropani`);
  }
  if (aana && aana > 0) {
    parts.push(`${aana} Aana`);
  }
  if (paisa && paisa > 0) {
    parts.push(`${paisa} Paisa`);
  }
  if (daam && daam > 0) {
    parts.push(`${daam} Daam`);
  }
  
  return parts.length > 0 ? parts.join(', ') : null;
};

/**
 * Extract Google Maps embed src URL from iframe code
 */
export const extractGoogleMapsUrl = (embedCode: string): string | null => {
  if (!embedCode) return null;
  
  // If it's already a URL, return it
  if (embedCode.startsWith('http')) {
    return embedCode;
  }
  
  // Extract src from iframe
  const srcMatch = embedCode.match(/src="([^"]*)"/);
  if (srcMatch && srcMatch[1]) {
    return srcMatch[1];
  }
  
  return null;
};

/**
 * Validate land area values
 */
export const validateLandArea = (
  ropani?: number,
  aana?: number,
  paisa?: number,
  daam?: number
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (ropani !== undefined && (ropani < 0 || !Number.isInteger(ropani))) {
    errors.push('Ropani must be a non-negative integer');
  }
  
  if (aana !== undefined && (aana < 0 || aana > 15 || !Number.isInteger(aana))) {
    errors.push('Aana must be between 0 and 15');
  }
  
  if (paisa !== undefined && (paisa < 0 || paisa > 3 || !Number.isInteger(paisa))) {
    errors.push('Paisa must be between 0 and 3');
  }
  
  if (daam !== undefined && (daam < 0 || daam > 3 || !Number.isInteger(daam))) {
    errors.push('Daam must be between 0 and 3');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get the best area display for a property
 * Prioritizes land area display over formatted area
 */
export const getBestAreaDisplay = (property: Property): string => {
  if (property.land_area_display) {
    return property.land_area_display;
  }
  
  if (property.formatted_land_area && property.formatted_land_area !== '0-0-0-0') {
    const { ropani, aana, paisa, daam } = parseLandArea(property.formatted_land_area);
    const display = getLandAreaDisplay(ropani, aana, paisa, daam);
    if (display) return display;
  }
  
  if (property.formatted_area) {
    return property.formatted_area;
  }
  
  return `${property.area} ${property.area_unit || 'sqft'}`;
};

/**
 * Check if property has land area information
 */
export const hasLandAreaInfo = (property: Property): boolean => {
  return !!(
    property.land_ropani ||
    property.land_aana ||
    property.land_paisa ||
    property.land_daam ||
    property.land_area_display ||
    property.formatted_land_area
  );
};

/**
 * Check if property has Google Maps embed
 */
export const hasGoogleMapsEmbed = (property: Property): boolean => {
  return !!(property.google_maps_embed_url || property.google_maps_embed_src);
};

/**
 * Get Google Maps embed URL for iframe
 */
export const getGoogleMapsEmbedUrl = (property: Property): string | null => {
  if (property.google_maps_embed_src) {
    return property.google_maps_embed_src;
  }
  
  if (property.google_maps_embed_url) {
    return extractGoogleMapsUrl(property.google_maps_embed_url);
  }
  
  return null;
};
