interface OrganizationData {
  id: number;
  name: string;
  description: string;
  logo: string;
  phone: string;
  email: string;
  address: string;
  whatsapp: string;
  facebook: string;
  instagram: string;
  linkedin: string;
  twitter: string;
}

export const updateMetaTags = (orgData: OrganizationData) => {
  console.log('Updating meta tags with organization data:', orgData);

  // Update page title
  const title = `${orgData.name} - Luxury Real Estate`;
  document.title = title;

  // Update meta description
  const description = orgData.description || `${orgData.name} specializes in luxury real estate sales, investment consulting, and property management. Find your dream property with our expert agents.`;

  // Get current URL for social sharing
  const currentUrl = window.location.href;

  // Extract Twitter handle from URL
  const twitterHandle = orgData.twitter ?
    (orgData.twitter.includes('twitter.com') || orgData.twitter.includes('x.com') ?
      `@${orgData.twitter.split('/').pop()}` :
      orgData.twitter.startsWith('@') ? orgData.twitter : `@${orgData.twitter}`)
    : '@realestate';

  // Update various meta tags
  const metaUpdates = [
    { id: 'page-title', content: title },
    { id: 'page-description', content: description },
    { id: 'page-author', content: orgData.name },
    { id: 'og-url', content: currentUrl },
    { id: 'og-title', content: title },
    { id: 'og-description', content: description },
    { id: 'og-site-name', content: orgData.name },
    { id: 'twitter-url', content: currentUrl },
    { id: 'twitter-title', content: title },
    { id: 'twitter-description', content: description },
    { id: 'twitter-site', content: twitterHandle },
    { id: 'twitter-creator', content: twitterHandle },
    { id: 'canonical-url', href: currentUrl }
  ];

  metaUpdates.forEach(({ id, content, href }) => {
    const element = document.getElementById(id);
    if (element) {
      if (element.tagName === 'TITLE') {
        element.textContent = content || '';
      } else if (element.tagName === 'LINK' && href) {
        element.setAttribute('href', href);
      } else if (content) {
        element.setAttribute('content', content);
      }
    }
  });

  // Update Open Graph and Twitter images if logo is available
  if (orgData.logo) {
    const ogImage = document.getElementById('og-image');
    const twitterImage = document.getElementById('twitter-image');

    // Construct proper logo URL for social media sharing
    let logoUrl = orgData.logo;

    // Handle Django media URLs
    if (logoUrl.startsWith('/media/')) {
      // For Django media files, construct full URL
      logoUrl = `${window.location.origin}${logoUrl}`;
    } else if (logoUrl.startsWith('media/')) {
      // Handle relative media paths
      logoUrl = `${window.location.origin}/${logoUrl}`;
    } else if (!logoUrl.startsWith('http')) {
      // Handle other relative paths
      logoUrl = logoUrl.startsWith('/') ?
        `${window.location.origin}${logoUrl}` :
        `${window.location.origin}/${logoUrl}`;
    }

    console.log('Updating social media image to:', logoUrl);

    if (ogImage) {
      ogImage.setAttribute('content', logoUrl);
    }
    if (twitterImage) {
      twitterImage.setAttribute('content', logoUrl);
    }
  }

  // Update favicon if logo is available
  if (orgData.logo) {
    const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
    if (favicon) {
      let logoUrl = orgData.logo;

      // Handle Django media URLs for favicon
      if (logoUrl.startsWith('/media/')) {
        logoUrl = `${window.location.origin}${logoUrl}`;
      } else if (logoUrl.startsWith('media/')) {
        logoUrl = `${window.location.origin}/${logoUrl}`;
      } else if (!logoUrl.startsWith('http')) {
        logoUrl = logoUrl.startsWith('/') ?
          `${window.location.origin}${logoUrl}` :
          `${window.location.origin}/${logoUrl}`;
      }

      favicon.href = logoUrl;
    }
  }
};
