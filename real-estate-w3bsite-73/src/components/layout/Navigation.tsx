import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';

import { Button } from '@/components/ui/button';
import logo from '@/assets/logo.png'; // Fallback logo
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const navigationItems = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Services', href: '/services' },
  { name: 'Properties', href: '/properties' },
  { name: 'Our Agents', href: '/agents' },
  { name: 'Gallery', href: '/gallery' },
  { name: 'News', href: '/news' },
  { name: 'Contact', href: '/contact' },
];

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuth();
  const { organization, loading: orgLoading } = useOrganization();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActive = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled
          ? "bg-white/60 backdrop-blur-[6px] border-b border-white/60 "
          : "bg-white border-b border-transparent"
      )}
    >
      <nav className="container mx-auto px-4 lg:px-8">
        <div className="flex items-center justify-between h-20 md:h-[88px]">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <img
              src={organization?.logo || logo}
              alt={`${organization?.name || 'Premier Properties'} Logo`}
              className="w-16 h-16 rounded-xl object-cover"
            />
            <div>
              <h1 className="font-extrabold text-2xl text-navy-dark leading-none">
                {organization?.name || (
                  <>Estate<span className="text-gold-primary">Agency</span></>
                )}
              </h1>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-6">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "text-base font-semibold tracking-wide transition duration-200 relative py-2",
                  isActive(item.href)
                    ? "text-navy-primary"
                    : "text-gray-medium hover:text-gold-primary"
                )}
              >
                {item.name}
                {isActive(item.href) && (
                  <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gold-primary rounded-full" />
                )}
              </Link>
            ))}
            
            {/* Auth Section */}
            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="ml-4">
                    {user?.name} ▼
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48 bg-white">
                  <DropdownMenuItem asChild>
                    <Link to={user?.roles === 'admin' ? '/admin-dashboard' : '/customer-dashboard'}>
                      My Dashboard
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={logout}>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center ml-4">
                <Link to="/auth">
                  <Button variant="premium" size="lg">
                    Get Started
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="lg:hidden p-3 rounded-lg hover:bg-gold-primary/10 transition-all duration-300"
            aria-label="Toggle menu"
          >
            <div className="w-6 h-6 flex flex-col justify-center items-center">
              <span
                className={cn(
                  "bg-navy-dark block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm",
                  isOpen ? "rotate-45 translate-y-1" : "-translate-y-0.5"
                )}
              />
              <span
                className={cn(
                  "bg-navy-dark block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm my-0.5",
                  isOpen ? "opacity-0" : "opacity-100"
                )}
              />
              <span
                className={cn(
                  "bg-navy-dark block transition-all duration-300 ease-out h-0.5 w-6 rounded-sm",
                  isOpen ? "-rotate-45 -translate-y-1" : "translate-y-0.5"
                )}
              />
            </div>
          </button>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <div className="lg:hidden py-5 border-t border-gold-primary/30 bg-white/90 backdrop-blur-md rounded-b-xl shadow-md">
            <div className="flex flex-col space-y-3 px-4">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsOpen(false)}
                  className={cn(
                    "text-base font-medium px-4 py-3 rounded-md transition-all duration-200",
                    isActive(item.href)
                      ? "text-navy-primary bg-gold-primary/10"
                      : "text-navy-dark hover:text-gold-primary hover:bg-gold-primary/5"
                  )}
                >
                  {item.name}
                </Link>
              ))}
              
              {/* Mobile Auth Section */}
              <div className="border-t border-gold-primary/20 pt-3 mt-3">
                {isAuthenticated ? (
                  <div className="space-y-2">
                    <Link
                      to={user?.roles === 'admin' ? '/admin-dashboard' : '/customer-dashboard'}
                      onClick={() => setIsOpen(false)}
                      className="block px-4 py-3 text-navy-primary font-medium rounded-md bg-gold-primary/10"
                    >
                      My Dashboard
                    </Link>
                    <button
                      onClick={() => {
                        logout();
                        setIsOpen(false);
                      }}
                      className="block w-full text-left px-4 py-3 text-navy-dark rounded-md hover:bg-gold-primary/5"
                    >
                      Logout
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Link
                      to="/auth"
                      onClick={() => setIsOpen(false)}
                      className="block w-full text-center px-4 py-3 text-white bg-gold-primary rounded-md hover:bg-gold-dark transition-colors"
                    >
                      Get Started
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </nav>
      

    </header>
  );
}
