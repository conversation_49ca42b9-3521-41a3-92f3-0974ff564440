import React from 'react';
import { Link } from 'react-router-dom';
import { useOrganization } from '@/contexts/OrganizationContext';

export const Footer = () => {
  const { organization, loading } = useOrganization();

  // Fallback data while loading or if API fails
  const displayData = organization || {
    name: 'Premier Properties',
    description: 'Your trusted partner in luxury real estate. We specialize in premium properties and provide exceptional service to help you find your dream home or investment.',
    phone: '9851422378',
    email: '<EMAIL>',
    address: 'Kathmandu, Nepal',
    facebook: '',
    instagram: '',
    linkedin: '',
    twitter: '',
    whatsapp: ''
  };

  return (
    <footer className="bg-navy-dark text-white">
      <div className="container mx-auto px-4 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              {organization?.logo ? (
                <img
                  src={organization.logo}
                  alt={displayData.name}
                  className="w-10 h-10 rounded-lg object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gradient-to-br from-gold-dark to-gold-primary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">
                    {displayData.name.split(' ').map(word => word[0]).join('').slice(0, 2)}
                  </span>
                </div>
              )}
              <div>
                <h3 className="font-bold text-xl">{displayData.name}</h3>
                <p className="text-sm text-gray-light">Luxury Real Estate</p>
              </div>
            </div>
            <p className="text-gray-light mb-6 max-w-md">
              {displayData.description}
            </p>
            <div className="flex space-x-4">
              {displayData.facebook && (
                <a
                  href={displayData.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                >
                  <span className="text-sm font-bold">f</span>
                </a>
              )}
              {displayData.linkedin && (
                <a
                  href={displayData.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                >
                  <span className="text-sm font-bold">in</span>
                </a>
              )}
              {displayData.instagram && (
                <a
                  href={displayData.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                >
                  <span className="text-sm font-bold">ig</span>
                </a>
              )}
              {displayData.twitter && (
                <a
                  href={displayData.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                >
                  <span className="text-sm font-bold">@</span>
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-lg mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><Link to="/properties" className="text-gray-light hover:text-white transition-colors">Properties</Link></li>
              <li><Link to="/agents" className="text-gray-light hover:text-white transition-colors">Our Agents</Link></li>
              <li><Link to="/services" className="text-gray-light hover:text-white transition-colors">Services</Link></li>
              <li><Link to="/about" className="text-gray-light hover:text-white transition-colors">About Us</Link></li>
              <li><Link to="/contact" className="text-gray-light hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="font-semibold text-lg mb-4">Contact Info</h4>
            <div className="space-y-3 text-gray-light">
              {displayData.phone && (
                <div>
                  <p className="font-medium text-white">Phone</p>
                  <p>{displayData.phone}</p>
                </div>
              )}
              {displayData.email && (
                <div>
                  <p className="font-medium text-white">Email</p>
                  <p>{displayData.email}</p>
                </div>
              )}
              {displayData.address && (
                <div>
                  <p className="font-medium text-white">Address</p>
                  <p>{displayData.address}</p>
                </div>
              )}
              {displayData.whatsapp && (
                <div>
                  <p className="font-medium text-white">WhatsApp</p>
                  <p>{displayData.whatsapp}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="border-t border-white/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-light text-sm">
            © {new Date().getFullYear()} {displayData.name.toUpperCase()}. All rights reserved.
          </p>
          <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-6 mt-4 md:mt-0">
           
            <div className="text-gray-light text-sm">
              Design and Developed by: 💙 |{" "}
              <a
                href="https://eimagineinfotech.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gold-primary hover:text-gold-light transition-colors font-medium"
              >
                Imagine Infotech
              </a>
              | नेपालमा बनेको
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};