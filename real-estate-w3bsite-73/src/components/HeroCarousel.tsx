import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Phone, Mail } from 'lucide-react';
import { getReq } from '@/apiService';
import { useOrganization } from '@/contexts/OrganizationContext';

interface Hero {
  id: string;
  title: string;
  subtitle: string;
  image: string;
  link_url?: string;
  is_active: boolean;
  order: number;
}

// Fallback data in case API fails
const fallbackHeroImages = [
  {
    id: "1",
    title: "Find Your Dream Home",
    subtitle: "Discover luxury properties in prime locations",
    image: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=1920",
    link_url: "/properties",
    is_active: true,
    order: 1
  },
  {
    id: "2",
    title: "Premium Properties Await",
    subtitle: "Experience luxury living at its finest",
    image: "https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=1920",
    link_url: "/properties",
    is_active: true,
    order: 2
  },
  {
    id: "3",
    title: "Your Perfect Investment",
    subtitle: "Building wealth through real estate",
    image: "https://images.unsplash.com/photo-1486718448742-163732cd1544?w=1920",
    link_url: "/contact",
    is_active: true,
    order: 3
  },
  {
    id: "4",
    title: "Trusted Real Estate Experts",
    subtitle: "Over 15 years of excellence in property sales",
    image: "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=1920",
    link_url: "/agents",
    is_active: true,
    order: 4
  }
];
export default function HeroCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [heroImages, setHeroImages] = useState<Hero[]>(fallbackHeroImages);
  const [loading, setLoading] = useState(true);
  const { organization } = useOrganization();

  // Fetch hero slides from API
  useEffect(() => {
    getReq('hero-slides/')
      .then((data) => {
        if (Array.isArray(data) && data.length > 0) {
          // Sort by order and filter active slides
          const activeSlides = data
            .filter(slide => slide.is_active)
            .sort((a, b) => a.order - b.order);
          setHeroImages(activeSlides);
        } else if (data === null) {
          console.error('HeroCarousel: API call returned null');
          // Keep fallback data
        } else {
          console.warn('HeroCarousel: No active hero slides found');
          // Keep fallback data
        }
      })
      .catch((err) => {
        console.error('HeroCarousel: Error fetching hero slides:', err);
        // Keep fallback data
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // Auto-rotate every 5 seconds
  useEffect(() => {
    if (!isAutoPlaying || loading) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, heroImages.length, loading]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroImages.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroImages.length) % heroImages.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Image Slides */}
      <div className="relative w-full h-full">
        {heroImages.map((image, index) => (
          <div
            key={image.id}
            className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image.image}
              alt={image.title}
              className="w-full h-full object-cover"
            />
            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/40"></div>
          </div>
        ))}
      </div>

      {/* Text Content Overlay */}
      <div className="absolute inset-0 flex items-center justify-center z-20">
        <div className="text-center text-white max-w-4xl mx-auto px-6">
          <h1 className="text-5xl md:text-6xl font-bold mb-4 leading-tight">
            {heroImages[currentSlide].title}
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-yellow-400 font-medium">
            {heroImages[currentSlide].subtitle}
          </p>

          {/* Call to Action Buttons - Constant buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              to="/properties"
              className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 inline-block"
            >
              See Properties
            </Link>
            <Link
              to="/contact"
              className="border-2 border-white text-white hover:bg-white hover:text-black font-semibold px-8 py-3 rounded-lg transition-all duration-300 inline-block"
            >
              Contact Us
            </Link>
          </div>

          {/* Contact Info */}
          <div className="mt-8 flex flex-col sm:flex-row gap-6 justify-center items-center text-sm">
            <div className="flex items-center gap-2">
              <Phone size={16} />
              <span>{organization?.phone || '+****************'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail size={16} />
              <span>{organization?.email || '<EMAIL>'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-30 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300 hover:scale-110"
        aria-label="Previous image"
      >
        <ChevronLeft size={24} />
      </button>
      
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-30 p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300 hover:scale-110"
        aria-label="Next image"
      >
        <ChevronRight size={24} />
      </button>

      {/* Navigation Dots */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-30 flex space-x-3">
        {heroImages.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide
                ? 'bg-yellow-500 scale-125'
                : 'bg-white/50 hover:bg-white/70'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Slide Progress Indicator */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20 z-30">
        <div 
          className="h-full bg-yellow-500 transition-all duration-300 ease-linear"
          style={{ width: `${((currentSlide + 1) / heroImages.length) * 100}%` }}
        ></div>
      </div>
    </div>
  );
}