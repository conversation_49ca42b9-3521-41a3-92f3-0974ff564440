import { useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';

interface SEOHeadProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  image,
  url
}) => {
  const { organization, loading } = useOrganization();

  // Static fallback data
  const staticOrgData = {
    name: 'सिमथली रियल स्टेट',
    description: 'सिमथली रियल स्टेट सबैका लागि सम्पत्तिको सुरक्षा र सुनिश्चितता प्रदान गर्न प्रतिबद्ध छ। हामी विश्वास गर्छौं कि प्रत्येक व्यक्तिलाई आफ्नो सम्पत्तिको अधिकार हुनुपर्छ।',
    logo: '/image.png',
    twitter: '@simthalirealestate'
  };

  useEffect(() => {
    // Use organization data if available, otherwise use static data
    const orgData = organization || staticOrgData;

    // Use provided props or fall back to organization data
    const pageTitle = title || `${orgData.name} - Real Estate`;
    const pageDescription = description || orgData.description;
      const pageUrl = url || window.location.href;
      
      // Update document title
      document.title = pageTitle;
      
      // Extract Twitter handle
      const twitterHandle = orgData.twitter ?
        (orgData.twitter.includes('twitter.com') || orgData.twitter.includes('x.com') ?
          `@${orgData.twitter.split('/').pop()}` :
          orgData.twitter.startsWith('@') ? orgData.twitter : `@${orgData.twitter}`)
        : '@simthalirealestate';

      // Construct logo URL
      let logoUrl = image || orgData.logo || '/image.png';
      if (logoUrl && !logoUrl.startsWith('http')) {
        if (logoUrl.startsWith('/media/')) {
          logoUrl = `${window.location.origin}${logoUrl}`;
        } else if (logoUrl.startsWith('media/')) {
          logoUrl = `${window.location.origin}/${logoUrl}`;
        } else if (!logoUrl.startsWith('/')) {
          logoUrl = `${window.location.origin}/${logoUrl}`;
        } else {
          logoUrl = `${window.location.origin}${logoUrl}`;
        }
      }

      // Update meta tags
      const metaUpdates = [
        { id: 'page-title', content: pageTitle },
        { id: 'page-description', content: pageDescription },
        { id: 'page-author', content: orgData.name },
        { id: 'og-url', content: pageUrl },
        { id: 'og-title', content: pageTitle },
        { id: 'og-description', content: pageDescription },
        { id: 'og-site-name', content: orgData.name },
        { id: 'og-image', content: logoUrl },
        { id: 'twitter-url', content: pageUrl },
        { id: 'twitter-title', content: pageTitle },
        { id: 'twitter-description', content: pageDescription },
        { id: 'twitter-image', content: logoUrl },
        { id: 'twitter-site', content: twitterHandle },
        { id: 'twitter-creator', content: twitterHandle },
        { id: 'canonical-url', href: pageUrl }
      ];

      metaUpdates.forEach(({ id, content, href }) => {
        const element = document.getElementById(id);
        if (element) {
          if (element.tagName === 'TITLE') {
            element.textContent = content || '';
          } else if (element.tagName === 'LINK' && href) {
            element.setAttribute('href', href);
          } else if (content) {
            element.setAttribute('content', content);
          }
        }
      });

      // Update favicon
      const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
      if (favicon && logoUrl) {
        favicon.href = logoUrl;
      }

      console.log('SEO meta tags updated:', {
        title: pageTitle,
        description: pageDescription,
        image: logoUrl,
        url: pageUrl,
        organization: orgData.name
      });
  }, [organization, loading, title, description, image, url]);

  return null; // This component doesn't render anything
};
