import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Schemas
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
});

const registerSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  phone: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type LoginFormData = z.infer<typeof loginSchema>;
type RegisterFormData = z.infer<typeof registerSchema>;

interface AuthDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultTab?: 'login' | 'register';
}

export const AuthDialog: React.FC<AuthDialogProps> = ({
  open,
  onOpenChange,
  defaultTab = 'login',
}) => {
  const [activeTab, setActiveTab] = useState<'login' | 'register'>(defaultTab);
  const [isLoading, setIsLoading] = useState(false);
  const { login, register } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const loginForm = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const registerForm = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
    },
  });

  const onLoginSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      console.log('Attempting login with:', data.username);
      const success = await login(data.username, data.password);
      
      if (success) {
        toast({
          title: 'Welcome back!',
          description: 'You have successfully logged in.',
          duration: 3000,
        });
        onOpenChange(false);
        loginForm.reset();

        // Small delay to ensure user state is updated, then redirect based on role
        setTimeout(() => {
          const storedUser = localStorage.getItem('user');
          if (storedUser) {
            const userData = JSON.parse(storedUser);
            if (userData.roles === 'admin') {
              navigate('/admin-dashboard');
            } else {
              navigate('/customer-dashboard');
            }
          } else {
            // Fallback: refresh page if user data not found
            window.location.reload();
          }
        }, 100);
      } else {
        throw new Error('Login failed: Invalid response from server');
      }
    } catch (error: any) {
      let errorMessage = 'An unexpected error occurred during login.';
      
      // Handle specific error cases
      if (error.message.includes('Failed to get CSRF token')) {
        errorMessage = 'Unable to establish a secure connection. Please try again.';
      } else if (error.message.includes('Invalid credentials')) {
        errorMessage = 'Invalid username or password. Please try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: 'Login failed',
        description: errorMessage,
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onRegisterSubmit = async (data: RegisterFormData) => {
    setIsLoading(true);
    try {
      const success = await register({
        name: data.name,
        email: data.email,
        roles: 'customer', // RegisterDialog only handles customer registration
        phone: data.phone,
        password: data.password,
      });

      if (success) {
        toast({
          title: 'Account created!',
          description: 'Welcome to Premier Properties. You are now logged in.',
        });
        onOpenChange(false);
        registerForm.reset();
      } else {
        toast({
          title: 'Registration failed',
          description: 'Please check your information and try again. Make sure your email is unique and password meets requirements.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Registration error in component:', error);
      toast({
        title: 'Registration failed',
        description: 'An error occurred while creating your account. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const switchTab = (tab: 'login' | 'register') => {
    setActiveTab(tab);
    // Reset forms when switching tabs
    loginForm.reset();
    registerForm.reset();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[480px] p-0 overflow-hidden">
        {/* Tab Header */}
        <div className="relative bg-gradient-to-r from-navy-primary to-navy-dark">
          <div className="flex">
            <button
              onClick={() => switchTab('login')}
              className={cn(
                "flex-1 py-4 px-6 text-center font-semibold transition-all duration-300 relative",
                activeTab === 'login' 
                  ? "text-white" 
                  : "text-white/70 hover:text-white/90"
              )}
            >
              Sign In
              {activeTab === 'login' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gold-primary rounded-t-full transition-all duration-300" />
              )}
            </button>
            <button
              onClick={() => switchTab('register')}
              className={cn(
                "flex-1 py-4 px-6 text-center font-semibold transition-all duration-300 relative",
                activeTab === 'register' 
                  ? "text-white" 
                  : "text-white/70 hover:text-white/90"
              )}
            >
              Sign Up
              {activeTab === 'register' && (
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gold-primary rounded-t-full transition-all duration-300" />
              )}
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="relative overflow-hidden">
          {/* Login Form */}
          <div className={cn(
            "transition-all duration-500 ease-in-out",
            activeTab === 'login' 
              ? "translate-x-0 opacity-100" 
              : "-translate-x-full opacity-0 absolute inset-0"
          )}>
            <div className="p-6">
              <DialogHeader className="mb-6">
                <DialogTitle className="text-2xl font-bold text-navy-primary">
                  Welcome Back
                </DialogTitle>
                <DialogDescription>
                  Sign in to your account to access your dashboard.
                </DialogDescription>
              </DialogHeader>

              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                  <FormField
                    control={loginForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="Enter your password" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-4 pt-2">
                    <Button
                      type="submit"
                      className="w-full"
                      variant="premium"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Signing in...' : 'Sign In'}
                    </Button>

                    <div className="text-center">
                      <Button
                        type="button"
                        variant="link"
                        onClick={() => switchTab('register')}
                        className="text-sm text-navy-primary hover:text-gold-primary"
                      >
                        Don't have an account? Sign up
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>

          {/* Register Form */}
          <div className={cn(
            "transition-all duration-500 ease-in-out",
            activeTab === 'register' 
              ? "translate-x-0 opacity-100" 
              : "translate-x-full opacity-0 absolute inset-0"
          )}>
            <div className="p-6">
              <DialogHeader className="mb-6">
                <DialogTitle className="text-2xl font-bold text-navy-primary">
                  Create Account
                </DialogTitle>
                <DialogDescription>
                  Join Premier Properties to start your real estate journey.
                </DialogDescription>
              </DialogHeader>

              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                  <FormField
                    control={registerForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={registerForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter your phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={registerForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="Create password" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={registerForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Confirm Password</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="Confirm password" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-4 pt-2">
                    <Button
                      type="submit"
                      className="w-full"
                      variant="premium"
                      disabled={isLoading}
                    >
                      {isLoading ? 'Creating account...' : 'Create Account'}
                    </Button>

                    <div className="text-center">
                      <Button
                        type="button"
                        variant="link"
                        onClick={() => switchTab('login')}
                        className="text-sm text-navy-primary hover:text-gold-primary"
                      >
                        Already have an account? Sign in
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
