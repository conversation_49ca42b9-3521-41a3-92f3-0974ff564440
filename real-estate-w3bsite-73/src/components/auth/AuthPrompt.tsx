import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Heart, MessageCircle, Calendar, User, ArrowRight } from 'lucide-react';

interface AuthPromptProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin: () => void;
  onRegister: () => void;
  feature: 'save' | 'contact' | 'schedule' | 'general';
  title?: string;
  description?: string;
}

const featureConfig = {
  save: {
    icon: Heart,
    title: 'Save Your Favorite Properties',
    description: 'Create an account to save properties and get notified about similar listings.',
    benefits: [
      'Save unlimited properties',
      'Get price drop alerts',
      'Compare saved properties',
      'Access your favorites anywhere'
    ]
  },
  contact: {
    icon: MessageCircle,
    title: 'Contact Our Agents',
    description: 'Sign in to contact our expert agents and get personalized assistance.',
    benefits: [
      'Direct agent communication',
      'Faster response times',
      'Personalized recommendations',
      'Track your inquiries'
    ]
  },
  schedule: {
    icon: Calendar,
    title: 'Schedule Property Visits',
    description: 'Create an account to schedule visits and manage your appointments.',
    benefits: [
      'Easy appointment scheduling',
      'Calendar integration',
      'Visit reminders',
      'Reschedule flexibility'
    ]
  },
  general: {
    icon: User,
    title: 'Join Premier Properties',
    description: 'Create your account to access all premium features.',
    benefits: [
      'Personalized experience',
      'Exclusive property alerts',
      'Priority support',
      'Advanced search filters'
    ]
  }
};

export const AuthPrompt: React.FC<AuthPromptProps> = ({
  isOpen,
  onClose,
  onLogin,
  onRegister,
  feature,
  title,
  description
}) => {
  const config = featureConfig[feature];
  const IconComponent = config.icon;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-navy-primary/10 rounded-lg">
              <IconComponent className="h-6 w-6 text-navy-primary" />
            </div>
            <DialogTitle className="text-xl">
              {title || config.title}
            </DialogTitle>
          </div>
          <DialogDescription className="text-gray-medium">
            {description || config.description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Benefits */}
          <Card className="border-navy-primary/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm text-navy-primary">
                What you'll get:
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <ul className="space-y-2">
                {config.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm">
                    <div className="w-1.5 h-1.5 bg-navy-primary rounded-full flex-shrink-0" />
                    <span className="text-gray-dark">{benefit}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button 
              onClick={onRegister}
              className="w-full bg-navy-primary hover:bg-navy-dark text-white"
              size="lg"
            >
              Create Free Account
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-medium">
                  Already have an account?
                </span>
              </div>
            </div>
            
            <Button 
              onClick={onLogin}
              variant="outline"
              className="w-full"
              size="lg"
            >
              Sign In
            </Button>
          </div>

          {/* Quick Benefits */}
          <div className="text-center pt-2">
            <p className="text-xs text-gray-medium">
              Free to join • No spam • Unsubscribe anytime
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
