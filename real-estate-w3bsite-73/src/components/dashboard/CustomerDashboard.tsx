import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { ProfileManagement } from './customer/ProfileManagement';
import { SavedProperties } from './customer/SavedProperties';
import { CustomerInquiries } from './customer/CustomerInquiries';
import { ScheduledVisits } from './customer/ScheduledVisits';
import { PropertyAlerts } from './customer/PropertyAlerts';
import { CustomerDocuments } from './customer/CustomerDocuments';
import { CustomerMessages } from './customer/CustomerMessages';

export const CustomerDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  const sidebarItems = [
    { id: 'profile', label: 'Profile Management', icon: '👤' },
    { id: 'saved', label: 'Saved Properties', icon: '❤️' },
    { id: 'inquiries', label: 'My Inquiries', icon: '📨' },
    { id: 'alerts', label: 'Property Alerts', icon: '🔔' },
    { id: 'visits', label: 'Scheduled Visits', icon: '📅' },
    { id: 'documents', label: 'Documents', icon: '📄' },
    { id: 'messages', label: 'Messages', icon: '💬' },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileManagement />;
      case 'saved':
        return <SavedProperties />;
      case 'inquiries':
        return <CustomerInquiries />;
      case 'visits':
        return <ScheduledVisits />;
      case 'alerts':
        return <PropertyAlerts />;
      case 'documents':
        return <CustomerDocuments />;
      case 'messages':
        return <CustomerMessages />;
      default:
        return <div>Select a section from the sidebar</div>;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Header */}
        <div className="lg:hidden bg-background border-b p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-primary">Customer Dashboard</h2>
              <p className="text-muted-foreground text-sm">Welcome back, {user?.name}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setActiveTab(activeTab === 'profile' ? 'saved' : 'profile')}
            >
              Menu
            </Button>
          </div>
        </div>

        {/* Sidebar */}
        <div className="hidden lg:block w-64 bg-background shadow-lg min-h-screen border-r">
          <div className="p-6 border-b">
            <h2 className="text-xl font-bold text-primary">Customer Dashboard</h2>
            <p className="text-muted-foreground text-sm mt-1">Welcome back, {user?.name}</p>
          </div>
          <nav className="mt-6">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center px-6 py-3 text-left hover:bg-muted/50 transition-colors ${
                  activeTab === item.id ? 'bg-muted border-r-4 border-primary text-primary font-medium' : 'text-muted-foreground'
                }`}
              >
                <span className="mr-3 flex-shrink-0">{item.icon}</span>
                <span className="truncate">{item.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden">
          <div className="bg-background border-b p-2">
            <div className="flex overflow-x-auto space-x-2 pb-2">
              {sidebarItems.slice(0, 5).map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`flex-shrink-0 flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === item.id 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                >
                  <span className="mr-2">{item.icon}</span>
                  <span className="whitespace-nowrap">{item.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-4 lg:p-8 overflow-auto">
          <div className="max-w-full">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};