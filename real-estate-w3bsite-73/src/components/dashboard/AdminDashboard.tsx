import React, { useState } from 'react';
import { Building2, Users, Home, MessageSquare, Star, Calendar, Settings, BarChart3, User, MapPin, Tag, Image, Camera, Newspaper } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

// Import all admin management components
import { AdminOverview } from './admin/AdminOverview';
import { OrganizationManagement } from './admin/OrganizationManagement';
import { AboutUsManagement } from './admin/AboutUsManagement';
import { ServicesManagement } from './admin/ServicesManagement';
import { JourneyManagement } from './admin/JourneyManagement';
import { HeroManagement } from './admin/HeroManagement';
import { PropertiesManagement } from './admin/PropertiesManagement';
import { PropertyTypesManagement } from './admin/PropertyTypesManagement';
import { AgentsManagement } from './admin/AgentsManagement';
import { UserManagement } from './admin/UserManagement';
import { GalleryManagement } from './admin/GalleryManagement';
import { NewsManagement } from './admin/NewsManagement';
import { TeamManagement } from './admin/TeamManagement';
import { ContactManagement } from './admin/ContactManagement';

// import { NewsManagement } from './admin/NewsManagement';

const sidebarItems = [
  { id: 'overview', label: 'Overview', icon: <BarChart3 className="h-5 w-5" /> },
  { id: 'organization', label: 'Organization', icon: <Building2 className="h-5 w-5" /> },
  { id: 'about', label: 'About Us', icon: <User className="h-5 w-5" /> },
  { id: 'services', label: 'Services', icon: <Settings className="h-5 w-5" /> },
  { id: 'journey', label: 'Journey', icon: <Calendar className="h-5 w-5" /> },
  { id: 'hero', label: 'Hero Sections', icon: <Image className="h-5 w-5" /> },
  { id: 'properties', label: 'Properties', icon: <Home className="h-5 w-5" /> },
  { id: 'property-types', label: 'Property Types', icon: <Tag className="h-5 w-5" /> },
  { id: 'agents', label: 'Agents', icon: <Users className="h-5 w-5" /> },
  { id: 'team', label: 'Team', icon: <Users className="h-5 w-5" /> },
  { id: 'contacts', label: 'Contacts', icon: <MessageSquare className="h-5 w-5" /> },
  { id: 'gallery', label: 'Gallery', icon: <Camera className="h-5 w-5" /> },
  { id: 'news', label: 'News', icon: <Newspaper className="h-5 w-5" /> },
  { id: 'users', label: 'Users', icon: <User className="h-5 w-5" /> },
];

export const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const { user } = useAuth();

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <AdminOverview />;
      case 'organization':
        return <OrganizationManagement />;
      case 'about':
        return <AboutUsManagement />;
      case 'services':
        return <ServicesManagement />;
      case 'journey':
        return <JourneyManagement />;
      case 'hero':
        return <HeroManagement />;
      case 'properties':
        return <PropertiesManagement />;
      case 'property-types':
        return <PropertyTypesManagement />;
      case 'agents':
        return <AgentsManagement />;
      case 'team':
        return <TeamManagement />;
      case 'contacts':
        return <ContactManagement />;
      case 'gallery':
        return <GalleryManagement />;
      case 'news':
        return <NewsManagement />;
      case 'users':
        return <UserManagement />;
      default:
        return <div className="text-center text-muted-foreground">Select a section from the sidebar</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-light">
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Header */}
        <div className="lg:hidden bg-white border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-navy-primary">Admin Dashboard</h2>
              <p className="text-gray-medium text-sm">Welcome back, {user?.name}</p>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="hidden lg:block w-64 bg-white shadow-lg min-h-screen">
          <div className="p-6 border-b border-gold-primary/20 bg-gradient-to-r from-navy-primary to-navy-light">
            <h2 className="text-xl font-bold text-white">Admin Dashboard</h2>
            <p className="text-gold-light text-sm mt-1">Welcome back, {user?.name}</p>
          </div>
          <nav className="mt-6">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center px-6 py-3 text-left hover:bg-gold-primary/10 transition-all duration-300 ${
                  activeTab === item.id
                    ? 'bg-gradient-to-r from-gold-primary/20 to-gold-light/20 border-r-4 border-gold-primary text-navy-dark font-medium shadow-lg'
                    : 'text-gray-medium hover:text-navy-dark'
                }`}
              >
                <span className="mr-3 flex-shrink-0">{item.icon}</span>
                <span className="truncate">{item.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden">
          <div className="bg-white border-b border-gold-primary/20 p-3">
            {/* Scroll hint text */}
            <div className="text-xs text-gray-medium text-center mb-2">
              Swipe to see all sections
            </div>

            <div className="flex overflow-x-auto space-x-3 pb-3 scrollbar-hide">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`flex-shrink-0 flex flex-col items-center px-4 py-3 rounded-xl text-xs font-medium transition-all duration-300 min-w-[80px] ${
                    activeTab === item.id
                      ? 'bg-gradient-to-r from-gold-primary to-gold-light text-white shadow-lg transform scale-105'
                      : 'bg-gray-light text-gray-medium hover:bg-gold-primary/10 hover:text-navy-dark active:scale-95'
                  }`}
                >
                  <span className="mb-1 transform transition-transform duration-300">{item.icon}</span>
                  <span className="whitespace-nowrap text-center leading-tight">{item.label}</span>
                </button>
              ))}
            </div>

            {/* Mobile Navigation Indicator Dots */}
            <div className="flex justify-center mt-2">
              <div className="flex space-x-1 bg-gray-100 rounded-full px-2 py-1">
                {sidebarItems.map((item) => (
                  <button
                    key={`indicator-${item.id}`}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      activeTab === item.id
                        ? 'bg-gold-primary scale-125'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-3 sm:p-4 lg:p-8 overflow-auto">
          <div className="max-w-full min-h-screen">
            <div className="w-full">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};