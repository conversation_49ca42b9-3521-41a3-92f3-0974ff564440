import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Save, Upload } from 'lucide-react';
import { HeroSection } from '@/types';
import { getReq, postReq, postReqMultipart, putReq, deleteReq, putReqMultipart } from '@/apiService';

export const HeroManagement = () => {
  const [heroSections, setHeroSections] = useState<HeroSection[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});
  const [backgroundImageFile, setBackgroundImageFile] = useState<File | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getReq('admin/hero-slides/');
        setHeroSections(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('HeroManagement: Error fetching data:', err);
        setHeroSections([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleHeroDelete = async (heroId: string) => {
    if (confirm('Are you sure you want to delete this hero section?')) {
      setSaving(true);
      try {
        await deleteReq(`admin/hero-slides/${heroId}/`);
        setHeroSections(heroSections.filter(h => h.id !== heroId));
      } catch (error) {
        console.error('HeroManagement: Error deleting hero section:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleHeroEdit = (hero: HeroSection) => {
    setEditingItem(hero.id);
    setFormData(hero);
    setShowAddForm(true);
  };

  const handleHeroAdd = () => {
    setEditingItem(null);
    setFormData({
      title: '',
      subtitle: '',
      link_url: '',
      order: 0,
      is_active: true
    });
    setShowAddForm(true);
  };

  const handleHeroSave = async () => {
    setSaving(true);
    try {
      let heroData;

      const heroDataToSend = {
        title: formData.title,
        subtitle: formData.subtitle || '',
        link_url: formData.link_url || '',
        order: formData.order || 0,
        is_active: formData.is_active !== undefined ? formData.is_active : true
      };

      if (backgroundImageFile) {
        const formDataWithFile = new FormData();
        Object.keys(heroDataToSend).forEach(key => {
          formDataWithFile.append(key, heroDataToSend[key]);
        });
        formDataWithFile.append('image', backgroundImageFile);

        if (editingItem) {
          heroData = await putReqMultipart(`admin/hero-slides/${editingItem}/`, formDataWithFile);
        } else {
          heroData = await postReqMultipart('admin/hero-slides/', formDataWithFile);
        }
      } else {
        if (editingItem) {
          heroData = await putReq(`admin/hero-slides/${editingItem}/`, heroDataToSend);
        } else {
          heroData = await postReq('admin/hero-slides/', heroDataToSend);
        }
      }

      if (heroData && (heroData.status === "200" || heroData.status === "201")) {
        const updatedHeroSections = await getReq('admin/hero-slides/');
        if (Array.isArray(updatedHeroSections)) {
          setHeroSections(updatedHeroSections);
        }
      }

      setShowAddForm(false);
      setEditingItem(null);
      setFormData({});
      setBackgroundImageFile(null);
    } catch (error) {
      console.error('HeroManagement: Error saving hero section:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Hero Section Management</h2>
        <Button onClick={handleHeroAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Hero Section
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit Hero Section' : 'Add New Hero Section'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="hero-title">Title *</Label>
                <Input
                  id="hero-title"
                  value={formData.title || ''}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  placeholder="Enter hero title"
                />
              </div>
              <div>
                <Label htmlFor="hero-subtitle">Subtitle</Label>
                <Input
                  id="hero-subtitle"
                  value={formData.subtitle || ''}
                  onChange={(e) => setFormData({...formData, subtitle: e.target.value})}
                  placeholder="Enter hero subtitle"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="hero-link-url">Link URL</Label>
                <Input
                  id="hero-link-url"
                  value={formData.link_url || ''}
                  onChange={(e) => setFormData({...formData, link_url: e.target.value})}
                  placeholder="e.g., /properties or https://example.com"
                />
              </div>
              <div>
                <Label htmlFor="hero-order">Display Order</Label>
                <Input
                  id="hero-order"
                  type="number"
                  value={formData.order || 0}
                  onChange={(e) => setFormData({...formData, order: parseInt(e.target.value) || 0})}
                  placeholder="Enter display order (0 = first)"
                  min="0"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="hero-background">Background Image</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="hero-background"
                  type="file"
                  accept="image/*"
                  onChange={(e) => setBackgroundImageFile(e.target.files?.[0] || null)}
                  className="flex-1"
                />
                <Upload className="h-4 w-4 text-muted-foreground" />
              </div>
              {formData.image && (
                <p className="text-sm text-muted-foreground mt-1">Current: {formData.image}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="hero-active"
                checked={formData.is_active || false}
                onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
              />
              <Label htmlFor="hero-active">Active</Label>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleHeroSave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : (editingItem ? 'Update Hero Section' : 'Save Hero Section')}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                  setBackgroundImageFile(null);
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Hero Sections List</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead className="hidden md:table-cell">Subtitle</TableHead>
                  <TableHead className="hidden lg:table-cell">Button Text</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {heroSections.map((hero) => (
                  <TableRow key={hero.id}>
                    <TableCell className="font-medium">{hero.title}</TableCell>
                    <TableCell className="hidden md:table-cell">{hero.subtitle}</TableCell>
                    <TableCell className="hidden lg:table-cell">{hero.button_text}</TableCell>
                    <TableCell>
                      <Badge variant={hero.is_active ? "default" : "secondary"}>
                        {hero.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleHeroEdit(hero)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleHeroDelete(hero.id)}
                          disabled={saving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};