import React, { useState, useEffect } from 'react';
import { getReq, postReq, putReq } from '@/apiService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Save } from 'lucide-react';

// Updated interface to match Django API response
interface User {
  id: string;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  is_active: boolean;
  is_staff: boolean;
  date_joined: string;
}



export const UserManagement = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getReq('admin/users/');
        setUsers(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('UserManagement: Error fetching data:', err);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleUserDelete = async (userId: string) => {
    if (confirm('Are you sure you want to delete this user?')) {
      setSaving(true);
      try {
        await postReq(`admin/users/${userId}/`, { _method: 'DELETE' });
        setUsers(users.filter(u => u.id !== userId));
      } catch (error) {
        console.error('UserManagement: Error deleting user:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleUserEdit = (user: User) => {
    setEditingItem(user.id);
    setFormData(user);
    setShowAddForm(true);
  };

  const handleUserAdd = () => {
    setEditingItem(null);
    setFormData({
      username: '',
      email: '',
      first_name: '',
      last_name: '',
      phone_number: '',
      password: '',
      is_staff: false,
      is_active: true
    });
    setShowAddForm(true);
  };

  const handleUserSave = async () => {
    setSaving(true);
    try {
      const userData = {
        username: formData.username,
        email: formData.email,
        first_name: formData.first_name || '',
        last_name: formData.last_name || '',
        phone_number: formData.phone_number || '',
        is_staff: formData.is_staff || false,
        is_active: formData.is_active !== undefined ? formData.is_active : true
      };

      // Only include password for new users
      if (!editingItem && formData.password) {
        userData.password = formData.password;
      }

      let result;
      if (editingItem) {
        result = await putReq(`admin/users/${editingItem}/`, userData);
      } else {
        result = await postReq('admin/users/', userData);
      }

      if (result.status === "200" || result.status === "201") {
        // Refresh users list from API
        const updatedUsers = await getReq('admin/users/');
        if (Array.isArray(updatedUsers)) {
          setUsers(updatedUsers);
        }
      }
    } catch (error) {
      console.error('UserManagement: Error saving user:', error);
    } finally {
      setSaving(false);
    }
    setShowAddForm(false);
    setEditingItem(null);
    setFormData({});
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">User Management</h2>
        <Button onClick={handleUserAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit User' : 'Add New User'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="user-username">Username *</Label>
                <Input
                  id="user-username"
                  value={formData.username || ''}
                  onChange={(e) => setFormData({...formData, username: e.target.value})}
                  placeholder="Enter username"
                />
              </div>
              <div>
                <Label htmlFor="user-email">Email *</Label>
                <Input
                  id="user-email"
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  placeholder="Enter email address"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="user-first-name">First Name</Label>
                <Input
                  id="user-first-name"
                  value={formData.first_name || ''}
                  onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <Label htmlFor="user-last-name">Last Name</Label>
                <Input
                  id="user-last-name"
                  value={formData.last_name || ''}
                  onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                  placeholder="Enter last name"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="user-phone">Phone</Label>
                <Input
                  id="user-phone"
                  value={formData.phone_number || ''}
                  onChange={(e) => setFormData({...formData, phone_number: e.target.value})}
                  placeholder="Enter phone number"
                />
              </div>
              <div>
                <Label htmlFor="user-role">Role *</Label>
                <Select
                  value={formData.is_staff ? 'admin' : 'customer'}
                  onValueChange={(value) => setFormData({...formData, is_staff: value === 'admin'})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customer">Customer</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {!editingItem && (
              <div>
                <Label htmlFor="user-password">Password *</Label>
                <Input
                  id="user-password"
                  type="password"
                  value={formData.password || ''}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                  placeholder="Enter password"
                />
              </div>
            )}
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleUserSave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : editingItem ? 'Update User' : 'Save User'}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Users List</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead className="hidden md:table-cell">Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className="hidden lg:table-cell">Phone</TableHead>
                  <TableHead className="hidden lg:table-cell">Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="font-medium">
                      {user.first_name && user.last_name
                        ? `${user.first_name} ${user.last_name}`.trim()
                        : user.username || 'N/A'}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{user.email || 'N/A'}</TableCell>
                    <TableCell>
                      <Badge variant={user.is_staff ? "default" : "secondary"}>
                        {user.is_staff ? 'Admin' : 'Customer'}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">{user.phone_number || 'N/A'}</TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {user.date_joined ? new Date(user.date_joined).toLocaleDateString() : 'N/A'}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUserEdit(user)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUserDelete(user.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};