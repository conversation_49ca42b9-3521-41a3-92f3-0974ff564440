import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Save, Upload } from 'lucide-react';
import { Agent, User } from '@/types';
import { getReq, postReq, postReqMultipart, putReq, deleteReq } from '@/apiService';

export const AgentsManagement = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [propertyTypes, setPropertyTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        const [agentsData, usersData, propertyTypesData] = await Promise.all([
          getReq('admin/agents/'),
          getReq('admin/users/'),
          getReq('admin/property-types/') 
        ]);

        setAgents(Array.isArray(agentsData) ? agentsData : []);
        setUsers(Array.isArray(usersData) ? usersData : []);
        setPropertyTypes(Array.isArray(propertyTypesData) ? propertyTypesData : []);
      } catch (err) {
        console.error('AgentsManagement: Error fetching data:', err);
        setAgents([]);
        setUsers([]);
        setPropertyTypes([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleAgentDelete = async (agentId: string) => {
    if (confirm('Are you sure you want to delete this agent?')) {
      setSaving(true);
      try {
        await deleteReq(`admin/agents/${agentId}/`);
        setAgents(agents.filter(a => a.id !== agentId));
      } catch (error) {
        console.error('AgentsManagement: Error deleting agent:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleAgentEdit = (agent: Agent) => {
    setEditingItem(agent.id);
    setFormData(agent);
    setShowAddForm(true);
  };

  const handleAgentAdd = () => {
    setEditingItem(null);
    setFormData({
      user: '',
      experience_years: 0,
      license_number: '',
      specializations: [],
      is_active: true
    });
    setShowAddForm(true);
  };

  const handleAgentSave = async () => {
    // Validate required fields
    if (!formData.user) {
      alert('Please select a user for the agent');
      return;
    }

    setSaving(true);
    try {
      const agentDataToSend = {
        user: parseInt(formData.user),
        experience_years: parseInt(formData.experience_years) || 0,
        license_number: formData.license_number || '',
        is_active: formData.is_active !== undefined ? formData.is_active : true
      };

      let agentData: any;
      if (editingItem) {
        agentData = await putReq(`admin/agents/${editingItem}/`, agentDataToSend);
      } else {
        agentData = await postReq('admin/agents/', agentDataToSend);
      }

      // Check if the request was successful
      if (agentData && (agentData.status === "200" || agentData.status === "201" || agentData.data)) {
        const updatedAgents = await getReq('admin/agents/');
        if (Array.isArray(updatedAgents)) {
          setAgents(updatedAgents);
        }

        // Reset form and close dialog
        setShowAddForm(false);
        setEditingItem(null);
        setFormData({});
        setProfileImageFile(null);

        // Show success message
        alert(editingItem ? 'Agent updated successfully!' : 'Agent created successfully!');
      } else {
        // Handle API error response
        const errorMessage = agentData?.message || 'Failed to save agent. Please try again.';
        alert(errorMessage);
      }
    } catch (error) {
      console.error('AgentsManagement: Error saving agent:', error);
      alert('An error occurred while saving the agent. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Agents Management</h2>
        <Button onClick={handleAgentAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Agent
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit Agent' : 'Add New Agent'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="agent-user">Select User *</Label>
                <Select value={formData.user?.toString() || ''} onValueChange={(value) => setFormData({...formData, user: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a user to make agent" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.first_name} {user.last_name} ({user.username})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="agent-experience">Experience Years</Label>
                <Input
                  id="agent-experience"
                  type="number"
                  value={formData.experience_years || 0}
                  onChange={(e) => setFormData({...formData, experience_years: parseInt(e.target.value) || 0})}
                  placeholder="Years of experience"
                  min="0"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="agent-license">License Number</Label>
              <Input
                id="agent-license"
                value={formData.license_number || ''}
                onChange={(e) => setFormData({...formData, license_number: e.target.value})}
                placeholder="Enter license number"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="agent-active"
                checked={formData.is_active || false}
                onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
              />
              <Label htmlFor="agent-active">Active</Label>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleAgentSave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : (editingItem ? 'Update Agent' : 'Save Agent')}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                  setProfileImageFile(null);
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Agents List</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead className="hidden md:table-cell">Experience</TableHead>
                  <TableHead className="hidden lg:table-cell">License</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {agents?.map((agent) => (
                  <TableRow key={agent?.id}>
                    <TableCell className="font-medium">
                      {agent?.user_details ? 
                        `${agent.user_details.first_name || ''} ${agent.user_details.last_name || ''} ${agent.user_details.username ? `(${agent.user_details.username})` : ''}`.trim() : 
                        `User ID: ${agent?.user || 'N/A'}`
                      }
                    </TableCell>
                    
                    <TableCell>
                      <Badge variant={agent?.is_active ? "default" : "secondary"}>
                        {agent?.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleAgentEdit(agent)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleAgentDelete(agent.id)}
                          disabled={saving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};