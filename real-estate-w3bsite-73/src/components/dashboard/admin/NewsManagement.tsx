import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getReq, postReq, putReq, deleteReq, postReqMultipart, putReqMultipart } from '@/apiService';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, Eye, Star, Calendar, Image, Upload, X } from 'lucide-react';

interface NewsCategory {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
}

interface News {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image: string | null;
  category: NewsCategory | null;
  meta_title: string;
  meta_description: string;
  is_featured: boolean;
  is_published: boolean;
  published_at: string;
  created_at: string;
  updated_at: string;
}

export const NewsManagement = () => {
  const [news, setNews] = useState<News[]>([]);
  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [editingNews, setEditingNews] = useState<News | null>(null);
  const [editingCategory, setEditingCategory] = useState<NewsCategory | null>(null);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    category: '',
    meta_title: '',
    meta_description: '',
    is_featured: false,
    is_published: true
  });

  const [categoryFormData, setCategoryFormData] = useState({
    name: '',
    description: '',
    is_active: true
  });

  useEffect(() => {
    fetchNews();
    fetchCategories();
  }, []);

  const fetchNews = async () => {
    try {
      setLoading(true);
      const data = await getReq('admin/news/');
      const newsArray = Array.isArray(data) ? data : (data?.results || []);
      setNews(newsArray);
    } catch (error) {
      console.error('Error fetching news:', error);
      toast({
        title: "Error",
        description: "Failed to fetch news articles",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await getReq('admin/news-categories/');
      const categoriesArray = Array.isArray(data) ? data : (data?.results || []);
      setCategories(categoriesArray);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData({
      ...formData,
      title,
      slug: generateSlug(title),
      meta_title: title
    });
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = () => setImagePreview(reader.result as string);
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.title || !formData.content) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Title and Content)",
        variant: "destructive",
      });
      return;
    }

    try {
      const submitData = new FormData();

      // Add all form fields
      Object.entries(formData).forEach(([key, value]) => {
        if (key === 'category' && value) {
        submitData.append('category', String(value));
        } else if (typeof value === 'boolean') {
          submitData.append(key, value.toString());
        } else if (value) {
          submitData.append(key, value);
        }
      });

      // Add image if selected
      if (selectedImage) {
        submitData.append('featured_image', selectedImage);
      }

      let response: any;
      if (editingNews) {
        response = await putReqMultipart(`admin/news/${editingNews.id}/`, submitData);
      } else {
        response = await postReqMultipart('admin/news/', submitData);
      }

      // Check if the request was successful
      if (response && (response.status === "200" || response.status === "201" || response.data)) {
        toast({
          title: "Success",
          description: editingNews ? "News article updated successfully" : "News article created successfully",
        });

        setIsDialogOpen(false);
        resetForm();
        fetchNews();
      } else {
        // Handle API error response
        const errorMessage = response?.message || 'Failed to save news article. Please try again.';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error saving news:', error);
      toast({
        title: "Error",
        description: "An error occurred while saving the news article. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (newsItem: News) => {
    setEditingNews(newsItem);
    setFormData({
      title: newsItem.title,
      slug: newsItem.slug,
      excerpt: newsItem.excerpt,
      content: newsItem.content,
      category: newsItem.category?.id.toString() || '',
      meta_title: newsItem.meta_title,
      meta_description: newsItem.meta_description,
      is_featured: newsItem.is_featured,
      is_published: newsItem.is_published
    });
    if (newsItem.featured_image) {
      setImagePreview(newsItem.featured_image);
    }
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this news article?')) {
      try {
        const response = await deleteReq(`admin/news/${id}/`);

        // Check if the request was successful
        if (response && (response.status === "200" || response.status === "204")) {
          toast({
            title: "Success",
            description: "News article deleted successfully",
          });
          fetchNews();
        } else {
          // Handle API error response
          const errorMessage = response?.message || 'Failed to delete news article. Please try again.';
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error deleting news:', error);
        toast({
          title: "Error",
          description: "An error occurred while deleting the news article. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      category: '',
      meta_title: '',
      meta_description: '',
      is_featured: false,
      is_published: true
    });
    setEditingNews(null);
    setSelectedImage(null);
    setImagePreview(null);
  };

  // Category Management Functions
  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!categoryFormData.name) {
      toast({
        title: "Validation Error",
        description: "Please enter a category name",
        variant: "destructive",
      });
      return;
    }

    try {
      let response: any;
      if (editingCategory) {
        response = await putReq(`admin/news-categories/${editingCategory.id}/`, categoryFormData);
      } else {
        response = await postReq('admin/news-categories/', categoryFormData);
      }

      // Check if the request was successful
      if (response && (response.status === "200" || response.status === "201" || response.data)) {
        toast({
          title: "Success",
          description: editingCategory ? "Category updated successfully" : "Category created successfully",
        });

        setIsCategoryDialogOpen(false);
        resetCategoryForm();
        fetchCategories();
      } else {
        // Handle API error response
        const errorMessage = response?.message || 'Failed to save category. Please try again.';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error saving category:', error);
      toast({
        title: "Error",
        description: "An error occurred while saving the category. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCategoryEdit = (category: NewsCategory) => {
    setEditingCategory(category);
    setCategoryFormData({
      name: category.name,
      description: category.description,
      is_active: category.is_active
    });
    setIsCategoryDialogOpen(true);
  };

  const handleCategoryDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      try {
        const response = await deleteReq(`admin/news-categories/${id}/`);

        // Check if the request was successful
        if (response && (response.status === "200" || response.status === "204")) {
          toast({
            title: "Success",
            description: "Category deleted successfully",
          });
          fetchCategories();
        } else {
          // Handle API error response
          const errorMessage = response?.message || 'Failed to delete category. Please try again.';
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error deleting category:', error);
        toast({
          title: "Error",
          description: "An error occurred while deleting the category. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const resetCategoryForm = () => {
    setCategoryFormData({ name: '', description: '', is_active: true });
    setEditingCategory(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-navy-dark">News Management</h2>
          <p className="text-gray-medium">Manage news articles and categories</p>
        </div>
        
        <div className="flex gap-2">
          <Dialog open={isCategoryDialogOpen} onOpenChange={(open) => {
            setIsCategoryDialogOpen(open);
            if (!open) resetCategoryForm();
          }}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-gold-primary text-gold-primary hover:bg-gold-primary hover:text-white transition-all duration-300">
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
          </Dialog>

          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            setIsDialogOpen(open);
            if (!open) resetForm();
          }}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-navy-primary to-navy-light hover:from-navy-dark hover:to-navy-primary text-white shadow-lg hover:shadow-xl transition-all duration-300">
                <Plus className="h-4 w-4 mr-2" />
                Add News
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* News Articles Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {news.map((newsItem) => (
          <Card key={newsItem.id} className="overflow-hidden">
            {newsItem.featured_image && (
              <div className="aspect-video overflow-hidden">
                <img
                  src={newsItem.featured_image}
                  alt={newsItem.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg line-clamp-2">{newsItem.title}</CardTitle>
                  <CardDescription className="line-clamp-2 mt-2">
                    {newsItem.excerpt}
                  </CardDescription>
                </div>
                <div className="flex gap-1 ml-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(newsItem)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(newsItem.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  <Badge variant={newsItem.is_published ? "default" : "secondary"}>
                    {newsItem.is_published ? 'Published' : 'Draft'}
                  </Badge>
                  {newsItem.is_featured && (
                    <Badge variant="outline" className="text-gold-primary border-gold-primary bg-gold-primary/10">
                      <Star className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                  {newsItem.category && (
                    <Badge variant="outline">
                      {newsItem.category.name}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-medium">
                  <Calendar className="h-4 w-4 text-gold-primary" />
                  <span>{new Date(newsItem.published_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {news.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Eye className="h-12 w-12 mx-auto text-gold-primary mb-4" />
            <h3 className="text-lg font-medium text-navy-dark mb-2">No news articles yet</h3>
            <p className="text-gray-medium mb-4">Get started by creating your first news article</p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add News Article
            </Button>
          </CardContent>
        </Card>
      )}

      {/* News Article Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={(open) => {
        setIsDialogOpen(open);
        if (!open) resetForm();
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingNews ? 'Edit News Article' : 'Create New News Article'}
            </DialogTitle>
            <DialogDescription>
              {editingNews ? 'Update news article information' : 'Add a new news article to your website'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="media">Media</TabsTrigger>
                <TabsTrigger value="seo">SEO & Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleTitleChange(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="slug">Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="excerpt">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                    rows={3}
                    placeholder="Brief description of the article"
                  />
                </div>

                <div>
                  <Label htmlFor="content">Content *</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    rows={10}
                    required
                    placeholder="Write your article content here..."
                  />
                </div>
              </TabsContent>

              <TabsContent value="media" className="space-y-4">
                <div>
                  <Label>Featured Image</Label>
                  <div className="mt-2">
                    {imagePreview ? (
                      <div className="relative inline-block">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-48 h-32 object-cover rounded border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0"
                          onClick={() => {
                            setSelectedImage(null);
                            setImagePreview(null);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <div
                        className="border-2 border-dashed border-gray-medium/30 rounded-lg p-8 text-center cursor-pointer hover:border-gold-primary hover:bg-gold-primary/5 transition-all duration-300"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Image className="h-8 w-8 mx-auto text-gold-primary mb-2" />
                        <p className="text-sm text-navy-dark">Click to upload featured image</p>
                      </div>
                    )}
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageSelect}
                      className="hidden"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="seo" className="space-y-4">
                <div>
                  <Label htmlFor="meta_title">Meta Title</Label>
                  <Input
                    id="meta_title"
                    value={formData.meta_title}
                    onChange={(e) => setFormData({ ...formData, meta_title: e.target.value })}
                    placeholder="SEO title for search engines"
                  />
                </div>

                <div>
                  <Label htmlFor="meta_description">Meta Description</Label>
                  <Textarea
                    id="meta_description"
                    value={formData.meta_description}
                    onChange={(e) => setFormData({ ...formData, meta_description: e.target.value })}
                    rows={3}
                    placeholder="SEO description for search engines"
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_featured"
                      checked={formData.is_featured}
                      onChange={(e) => setFormData({ ...formData, is_featured: e.target.checked })}
                    />
                    <Label htmlFor="is_featured">Featured Article</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_published"
                      checked={formData.is_published}
                      onChange={(e) => setFormData({ ...formData, is_published: e.target.checked })}
                    />
                    <Label htmlFor="is_published">Published</Label>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2 pt-4">
              <Button type="submit" className="flex-1">
                {editingNews ? 'Update' : 'Create'} Article
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Category Management Dialog */}
      <Dialog open={isCategoryDialogOpen} onOpenChange={(open) => {
        setIsCategoryDialogOpen(open);
        if (!open) resetCategoryForm();
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </DialogTitle>
            <DialogDescription>
              {editingCategory ? 'Update category information' : 'Add a new news category'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleCategorySubmit} className="space-y-4">
            <div>
              <Label htmlFor="category-name">Name *</Label>
              <Input
                id="category-name"
                value={categoryFormData.name}
                onChange={(e) => setCategoryFormData({ ...categoryFormData, name: e.target.value })}
                required
              />
            </div>

            <div>
              <Label htmlFor="category-description">Description</Label>
              <Textarea
                id="category-description"
                value={categoryFormData.description}
                onChange={(e) => setCategoryFormData({ ...categoryFormData, description: e.target.value })}
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="category-active"
                checked={categoryFormData.is_active}
                onChange={(e) => setCategoryFormData({ ...categoryFormData, is_active: e.target.checked })}
              />
              <Label htmlFor="category-active">Active</Label>
            </div>

            <div className="flex gap-2 pt-4">
              <Button type="submit" className="flex-1">
                {editingCategory ? 'Update' : 'Create'} Category
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCategoryDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Categories List */}
      {categories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>News Categories</CardTitle>
            <CardDescription>Manage your news categories</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <h4 className="font-medium">{category.name}</h4>
                    {category.description && (
                      <p className="text-sm text-gray-500 mt-1">{category.description}</p>
                    )}
                    <Badge variant={category.is_active ? "default" : "secondary"} className="mt-2">
                      {category.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCategoryEdit(category)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCategoryDelete(category.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
