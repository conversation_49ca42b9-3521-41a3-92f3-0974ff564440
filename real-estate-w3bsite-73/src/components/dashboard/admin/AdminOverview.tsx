import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Home, MessageSquare, Users, Star } from 'lucide-react';
import { useState, useEffect } from 'react';
import { getReq } from '@/apiService';
import { Property, Contact, Agent, Achievements } from '@/types';

export const AdminOverview = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [achievements, setAchievements] = useState<Achievements | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [propertiesData, contactsData, agentsData, achievementsData] = await Promise.all([
          getReq('properties/'),
          getReq('admin/contacts/'),
          getReq('agents/'),
          getReq('admin/achievements/')
        ]);

        setProperties(Array.isArray(propertiesData) ? propertiesData : []);
        setContacts(Array.isArray(contactsData) ? contactsData : []);
        setAgents(Array.isArray(agentsData) ? agentsData : []);
        setAchievements(achievementsData || null);
      } catch (error) {
        console.error('AdminOverview: Error fetching data:', error);
        setError('Failed to load dashboard data. Please try again.');
        // Set empty arrays instead of mock data
        setProperties([]);
        setContacts([]);
        setAgents([]);
        setAchievements(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gold-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-navy-primary text-white rounded hover:bg-navy-dark"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-navy-dark">Dashboard Overview</h1>
        <p className="text-gray-medium">Welcome to your admin control panel</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-navy-primary hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-navy-dark">Total Properties</CardTitle>
            <Home className="h-4 w-4 text-navy-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-navy-dark">{properties.length}</div>
            <p className="text-xs text-gray-medium">
              +2 from last month
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-gold-primary hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-navy-dark">Contact Inquiries</CardTitle>
            <MessageSquare className="h-4 w-4 text-gold-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-navy-dark">{contacts.length}</div>
            <p className="text-xs text-gray-medium">
              +5 from last week
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-navy-light hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-navy-dark">Active Agents</CardTitle>
            <Users className="h-4 w-4 text-navy-light" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-navy-dark">{agents.filter(a => a.is_active).length}</div>
            <p className="text-xs text-gray-medium">
              All agents active
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-gold-dark hover:shadow-lg transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-navy-dark">Properties Sold</CardTitle>
            <Star className="h-4 w-4 text-gold-dark" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{achievements?.properties_sold?.toLocaleString() || '0'}</div>
            <p className="text-xs text-muted-foreground">
              Total achievements
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm">New contact inquiry from John Smith</p>
                <p className="text-xs text-muted-foreground">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm">Property "Luxury Downtown Apartment" marked as featured</p>
                <p className="text-xs text-muted-foreground">5 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm">New agent Sarah Johnson added to the team</p>
                <p className="text-xs text-muted-foreground">1 day ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};