import React, { useState, useEffect } from 'react';
import { getReq, postReq, putReq, deleteReq } from '@/apiService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Save } from 'lucide-react';
import { Service } from '@/types';

export const ServicesManagement = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getReq('admin/services/');
        setServices(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('ServicesManagement: Error fetching data:', err);
        setServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleServiceDelete = async (serviceId: string) => {
    if (confirm('Are you sure you want to delete this service?')) {
      setSaving(true);
      try {
        await deleteReq(`admin/services/${serviceId}/`);
        setServices(services.filter(s => s.id !== serviceId));
      } catch (error) {
        console.error('ServicesManagement: Error deleting service:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleServiceEdit = (service: Service) => {
    setEditingItem(service.id);
    setFormData(service);
    setShowAddForm(true);
  };

  const handleServiceAdd = () => {
    setEditingItem(null);
    setFormData({
      title: '',
      description: '',
      icon: '',
      order: 0,
      is_active: true
    });
    setShowAddForm(true);
  };

  const handleServiceSave = async () => {
    // Validate required fields
    if (!formData.title || !formData.description || !formData.icon) {
      alert('Please fill in all required fields (Title, Description, Icon)');
      return;
    }

    setSaving(true);
    try {
      const serviceData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        icon: formData.icon.trim(),
        order: parseInt(formData.order) || 0,
        is_active: formData.is_active !== undefined ? formData.is_active : true
      };

      let result: any;
      if (editingItem) {
        result = await putReq(`admin/services/${editingItem}/`, serviceData);
      } else {
        result = await postReq('admin/services/', serviceData);
      }

      // Check if the request was successful
      if (result && (result.status === "200" || result.status === "201" || result.data)) {
        // Refresh services list from API
        const updatedServices = await getReq('admin/services/');
        if (Array.isArray(updatedServices)) {
          setServices(updatedServices);
        }

        // Reset form and close dialog
        setShowAddForm(false);
        setEditingItem(null);
        setFormData({});

        // Show success message
        alert(editingItem ? 'Service updated successfully!' : 'Service created successfully!');
      } else {
        // Handle API error response
        const errorMessage = result?.message || 'Failed to save service. Please try again.';
        alert(errorMessage);
      }
    } catch (error) {
      console.error('ServicesManagement: Error saving service:', error);
      alert('An error occurred while saving the service. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Services Management</h2>
        <Button onClick={handleServiceAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Service
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit Service' : 'Add New Service'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="service-title">Title *</Label>
                <Input
                  id="service-title"
                  value={formData.title || ''}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  placeholder="Enter service title"
                />
              </div>
              <div>
                <Label htmlFor="service-icon">Icon *</Label>
                <Input
                  id="service-icon"
                  value={formData.icon || ''}
                  onChange={(e) => setFormData({...formData, icon: e.target.value})}
                  placeholder="Enter icon (e.g., 🏠, 📈, 🎯)"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="service-description">Description *</Label>
              <Textarea
                id="service-description"
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Enter service description"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="service-order">Display Order</Label>
              <Input
                id="service-order"
                type="number"
                value={formData.order || 0}
                onChange={(e) => setFormData({...formData, order: parseInt(e.target.value) || 0})}
                placeholder="Enter display order (0 = first)"
                min="0"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="service-active"
                checked={formData.is_active || false}
                onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
              />
              <Label htmlFor="service-active">Active</Label>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleServiceSave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : editingItem ? 'Update Service' : 'Save Service'}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Services List</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead className="hidden md:table-cell">Icon</TableHead>
                  <TableHead className="hidden lg:table-cell">Description</TableHead>
                  <TableHead className="hidden md:table-cell">Order</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {services.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell className="font-medium">{service.title}</TableCell>
                    <TableCell className="hidden md:table-cell text-2xl">{service.icon}</TableCell>
                    <TableCell className="hidden lg:table-cell max-w-xs">
                      <p className="truncate">{service.description}</p>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{service.order}</TableCell>
                    <TableCell>
                      <Badge variant={service.is_active ? "default" : "secondary"}>
                        {service.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleServiceEdit(service)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleServiceDelete(service.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};