import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Save } from 'lucide-react';
import { Journey } from '@/types';
import { getReq, postReq, putReq, deleteReq } from '@/apiService';

export const JourneyManagement = () => {
  const [journey, setJourney] = useState<Journey[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getReq('admin/journey-steps/');
        setJourney(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('JourneyManagement: Error fetching data:', err);
        setJourney([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleJourneyDelete = async (journeyId: string) => {
    if (confirm('Are you sure you want to delete this journey item?')) {
      setSaving(true);
      try {
        await deleteReq(`admin/journey-steps/${journeyId}/`);
        setJourney(journey.filter(j => j.id !== journeyId));
      } catch (error) {
        console.error('JourneyManagement: Error deleting journey item:', error);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleJourneyEdit = (journeyItem: Journey) => {
    setEditingItem(journeyItem.id);
    setFormData(journeyItem);
    setShowAddForm(true);
  };

  const handleJourneyAdd = () => {
    setEditingItem(null);
    setFormData({
      year: '',
      title: '',
      description: '',
      icon: '',
      order: 0,
      is_active: true
    });
    setShowAddForm(true);
  };

  const handleJourneySave = async () => {
    setSaving(true);
    try {
      let journeyData;

      if (editingItem) {
        journeyData = await putReq(`admin/journey-steps/${editingItem}/`, formData);
      } else {
        journeyData = await postReq('admin/journey-steps/', formData);
      }

      if (journeyData && (journeyData.status === "200" || journeyData.status === "201")) {
        const updatedJourney = await getReq('admin/journey-steps/');
        if (Array.isArray(updatedJourney)) {
          setJourney(updatedJourney);
        }
      }

      setShowAddForm(false);
      setEditingItem(null);
      setFormData({});
    } catch (error) {
      console.error('JourneyManagement: Error saving journey item:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Journey Management</h2>
        <Button onClick={handleJourneyAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Journey Item
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit Journey Item' : 'Add New Journey Item'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="journey-year">Year *</Label>
                <Input
                  id="journey-year"
                  type="number"
                  value={formData.year || ''}
                  onChange={(e) => setFormData({...formData, year: e.target.value})}
                  placeholder="Enter year (e.g., 2020)"
                  min="1900"
                  max="2100"
                />
              </div>
              <div>
                <Label htmlFor="journey-title">Title *</Label>
                <Input
                  id="journey-title"
                  value={formData.title || ''}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  placeholder="Enter milestone title"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="journey-description">Description *</Label>
              <Textarea
                id="journey-description"
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Describe this milestone in the company's journey"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="journey-icon">Icon *</Label>
                <Input
                  id="journey-icon"
                  value={formData.icon || ''}
                  onChange={(e) => setFormData({...formData, icon: e.target.value})}
                  placeholder="Enter icon name (e.g., 🏢, 📈, 🎯)"
                />
              </div>
              <div>
                <Label htmlFor="journey-order">Order</Label>
                <Input
                  id="journey-order"
                  type="number"
                  value={formData.order || 0}
                  onChange={(e) => setFormData({...formData, order: parseInt(e.target.value) || 0})}
                  placeholder="Display order"
                  min="0"
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleJourneySave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : (editingItem ? 'Update Journey Item' : 'Save Journey Item')}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Company Journey Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Year</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead className="hidden md:table-cell">Description</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {journey.sort((a, b) => {
                  const yearA = a.year ? parseInt(a.year.toString()) : 0;
                  const yearB = b.year ? parseInt(b.year.toString()) : 0;
                  return yearA - yearB;
                }).map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">{item.year || 'N/A'}</TableCell>
                    <TableCell>{item.title || 'Untitled'}</TableCell>
                    <TableCell className="hidden md:table-cell max-w-xs">
                      <p className="truncate">{item.description || 'No description'}</p>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleJourneyEdit(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleJourneyDelete(item.id)}
                          disabled={saving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};