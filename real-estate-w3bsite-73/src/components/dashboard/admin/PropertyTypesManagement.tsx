import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, Edit, Trash2, Save } from 'lucide-react';
import { PropertyType } from '@/types';
import { getReq, postReq, putReq, deleteReq } from '@/apiService';

export const PropertyTypesManagement = () => {
  const [propertyTypes, setPropertyTypes] = useState<PropertyType[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getReq('property-types/');
        setPropertyTypes(Array.isArray(data) ? data : []);
      } catch (err) {
        
        setPropertyTypes([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handlePropertyTypeDelete = async (typeId: string) => {
    if (confirm('Are you sure you want to delete this property type?')) {
      setSaving(true);
      try {
        const response = await deleteReq(`admin/property-types/${typeId}/`);

        // Check if the request was successful
        if (response && (response.status === "200" || response.status === "204")) {
          setPropertyTypes(propertyTypes.filter(pt => pt.id !== typeId));
          alert('Property type deleted successfully!');
        } else {
          // Handle API error response
          const errorMessage = response?.message || 'Failed to delete property type. Please try again.';
          alert(errorMessage);
        }
      } catch (error) {
        
        alert('An error occurred while deleting the property type. Please try again.');
      } finally {
        setSaving(false);
      }
    }
  };

  const handlePropertyTypeEdit = (propertyType: PropertyType) => {
    setEditingItem(propertyType.id);
    setFormData(propertyType);
    setShowAddForm(true);
  };

  const handlePropertyTypeAdd = () => {
    setEditingItem(null);
    setFormData({
      name: '',
      description: '',
      is_active: true
    });
    setShowAddForm(true);
  };

  const handlePropertyTypeSave = async () => {
    setSaving(true);
    try {
      let typeData;
      
      if (editingItem) {
        typeData = await putReq(`admin/property-types/${editingItem}/`, formData);
      } else {
        typeData = await postReq('admin/property-types/', formData);
      }

      if (typeData && (typeData.status === "200" || typeData.status === "201")) {
        const updatedTypes = await getReq('admin/property-types/');
        if (Array.isArray(updatedTypes)) {
          setPropertyTypes(updatedTypes);
        }
      }
      
      setShowAddForm(false);
      setEditingItem(null);
      setFormData({});
    } catch (error) {
      
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Property Types Management</h2>
        <Button onClick={handlePropertyTypeAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Property Type
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit Property Type' : 'Add New Property Type'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="type-name">Property Type Name *</Label>
              <Input
                id="type-name"
                value={formData.name || ''}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="Enter property type name"
              />
            </div>
            <div>
              <Label htmlFor="type-description">Description</Label>
              <Textarea
                id="type-description"
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Enter property type description"
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="type-active"
                checked={formData.is_active || false}
                onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
              />
              <Label htmlFor="type-active">Active</Label>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handlePropertyTypeSave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : (editingItem ? 'Update Property Type' : 'Save Property Type')}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Property Types List</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead className="hidden md:table-cell">Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {propertyTypes.map((type) => (
                  <TableRow key={type.id}>
                    <TableCell className="font-medium">{type.name}</TableCell>
                    <TableCell className="hidden md:table-cell max-w-xs">
                      <p className="truncate">{type.description}</p>
                    </TableCell>
                    <TableCell>
                      <Badge variant={type.is_active ? "default" : "secondary"}>
                        {type.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handlePropertyTypeEdit(type)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handlePropertyTypeDelete(type.id)}
                          disabled={saving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};