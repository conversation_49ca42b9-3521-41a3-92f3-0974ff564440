import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Save, Upload } from 'lucide-react';
import { useState, useEffect } from 'react';
import { getReq, putReq, putReqMultipart } from '@/apiService';
import { AboutUs } from '@/types';

export const AboutUsManagement = () => {
  const [aboutData, setAboutData] = useState({
    title: '',
    content: '',
    vision: '',
    mission: '',
    image1: null,
    image2: null,
    is_active: true
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [image1File, setImage1File] = useState<File | null>(null);
  const [image2File, setImage2File] = useState<File | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getReq('admin/about-us/');

        if (Array.isArray(data) && data.length > 0) {
          // If it's an array, take the first item
          setAboutData(data[0]);
        } else if (data && typeof data === 'object' && data.id) {
          // If it's a single object
          setAboutData(data);
        } else {
          // Set default empty about data
          setAboutData({
            title: '',
            content: '',
            vision: '',
            mission: '',
            image1: null,
            image2: null,
            is_active: true
          });
        }
      } catch (err) {
        console.error('AboutUsManagement: Error fetching data:', err);
        // Set default empty about data on error
        setAboutData({
          title: '',
          content: '',
          vision: '',
          mission: '',
          image1: null,
          image2: null,
          is_active: true
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleSave = async () => {
    setSaving(true);
    try {
      const hasFiles = image1File || image2File;

      if (hasFiles) {
        // Use multipart if there are files to upload
        const formData = new FormData();
        formData.append('title', aboutData.title);
        formData.append('content', aboutData.content);
        formData.append('vision', aboutData.vision);
        formData.append('mission', aboutData.mission);
        formData.append('is_active', aboutData.is_active.toString());

        // Add image files if selected
        if (image1File) {
          formData.append('image1', image1File);
        }
        if (image2File) {
          formData.append('image2', image2File);
        }

        const endpoint = aboutData.id ? `admin/about-us/${aboutData.id}/` : 'admin/about-us/1/';
        const result = await putReqMultipart(endpoint, formData);

        if (result.status === "200") {
          console.log('AboutUs updated successfully');
          if (!aboutData.id) {
            setAboutData({...aboutData, id: 1});
          }
        } else {
          console.error('AboutUsManagement: Error saving data:', result.message);
        }
      } else {
        // Use JSON if no files to upload
        const jsonData = {
          title: aboutData.title,
          content: aboutData.content,
          vision: aboutData.vision,
          mission: aboutData.mission,
          is_active: aboutData.is_active
        };

        const endpoint = aboutData.id ? `admin/about-us/${aboutData.id}/` : 'admin/about-us/1/';
        const result = await putReq(endpoint, jsonData);

        if (result.status === "200") {
          if (!aboutData.id) {
            setAboutData({...aboutData, id: 1});
          }
        } else {
        }
      }
    } catch (error) {
      console.error('AboutUsManagement: Error saving data:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>About Us Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="about-title">Title</Label>
          <Input id="about-title" value={aboutData.title} onChange={(e) => setAboutData({...aboutData, title: e.target.value})} />
        </div>
        <div>
          <Label htmlFor="about-content">Content</Label>
          <Textarea id="about-content" value={aboutData.content} onChange={(e) => setAboutData({...aboutData, content: e.target.value})} rows={4} />
        </div>
        <div>
          <Label htmlFor="about-vision">Vision</Label>
          <Textarea id="about-vision" value={aboutData.vision} onChange={(e) => setAboutData({...aboutData, vision: e.target.value})} rows={2} />
        </div>
        <div>
          <Label htmlFor="about-mission">Mission</Label>
          <Textarea id="about-mission" value={aboutData.mission} onChange={(e) => setAboutData({...aboutData, mission: e.target.value})} rows={2} />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="about-image1">Image 1</Label>
            <div className="space-y-2">
              <Input
                id="about-image1"
                type="file"
                accept="image/*"
                onChange={(e) => setImage1File(e.target.files?.[0] || null)}
              />
              {aboutData.image1 && (
                <p className="text-sm text-muted-foreground">
                  Current: {typeof aboutData.image1 === 'string' ? aboutData.image1.split('/').pop() : 'Image uploaded'}
                </p>
              )}
            </div>
          </div>
          <div>
            <Label htmlFor="about-image2">Image 2</Label>
            <div className="space-y-2">
              <Input
                id="about-image2"
                type="file"
                accept="image/*"
                onChange={(e) => setImage2File(e.target.files?.[0] || null)}
              />
              {aboutData.image2 && (
                <p className="text-sm text-muted-foreground">
                  Current: {typeof aboutData.image2 === 'string' ? aboutData.image2.split('/').pop() : 'Image uploaded'}
                </p>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Switch id="about-active" checked={aboutData.is_active} onCheckedChange={(checked) => setAboutData({...aboutData, is_active: checked})} />
          <Label htmlFor="about-active">Active</Label>
        </div>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="mr-2 h-4 w-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </CardContent>
    </Card>
  );
};