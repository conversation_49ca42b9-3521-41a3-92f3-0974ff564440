import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getReq, postReq, putReq, deleteReq, postReqMultipart } from '@/apiService';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash2, Image, Upload, X, ImageIcon, Check } from 'lucide-react';

interface GalleryImage {
  id: number;
  image: string;
  title: string;
  description: string;
  order: number;
  is_active: boolean;
}

interface Gallery {
  id: number;
  title: string;
  description: string;
  is_active: boolean;
  order: number;
  images: GalleryImage[];
  created_at: string;
}

export const GalleryManagement = () => {
  const [galleries, setGalleries] = useState<Gallery[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingGallery, setEditingGallery] = useState<Gallery | null>(null);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [selectedGallery, setSelectedGallery] = useState<Gallery | null>(null);
  const [uploadingImages, setUploadingImages] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [editingImage, setEditingImage] = useState<GalleryImage | null>(null);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  const [uploadedCount, setUploadedCount] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    order: 0,
    is_active: true
  });

  const [imageFormData, setImageFormData] = useState({
    title: '',
    description: '',
    order: 0,
    is_active: true
  });

  useEffect(() => {
    fetchGalleries();
  }, []);

  const fetchGalleries = async () => {
    try {
      setLoading(true);
      const data = await getReq('admin/gallery/');
      const galleriesArray = Array.isArray(data) ? data : (data?.results || []);
      setGalleries(galleriesArray);
    } catch (error) {
      
      toast({
        title: "Error",
        description: "Failed to fetch galleries",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.title) {
      toast({
        title: "Validation Error",
        description: "Please enter a gallery title",
        variant: "destructive",
      });
      return;
    }

    try {
      let response: any;
      if (editingGallery) {
        response = await putReq(`admin/gallery/${editingGallery.id}/`, formData);
      } else {
        response = await postReq('admin/gallery/', formData);
      }

      // Check if the request was successful
      if (response && (response.status === "200" || response.status === "201" || response.data)) {
        toast({
          title: "Success",
          description: editingGallery ? "Gallery updated successfully" : "Gallery created successfully",
        });

        setIsDialogOpen(false);
        setEditingGallery(null);
        setFormData({ title: '', description: '', order: 0, is_active: true });
        fetchGalleries();
      } else {
        // Handle API error response
        const errorMessage = response?.message || 'Failed to save gallery. Please try again.';
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
     
      toast({
        title: "Error",
        description: "An error occurred while saving the gallery. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (gallery: Gallery) => {
    setEditingGallery(gallery);
    setFormData({
      title: gallery.title,
      description: gallery.description,
      order: gallery.order,
      is_active: gallery.is_active
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this gallery?')) {
      try {
        const response = await deleteReq(`admin/gallery/${id}/`);

        // Check if the request was successful
        if (response && (response.status === "200" || response.status === "204")) {
          toast({
            title: "Success",
            description: "Gallery deleted successfully",
          });
          fetchGalleries();
        } else {
          // Handle API error response
          const errorMessage = response?.message || 'Failed to delete gallery. Please try again.';
          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
      } catch (error) {
      
        toast({
          title: "Error",
          description: "An error occurred while deleting the gallery. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const resetForm = () => {
    setFormData({ title: '', description: '', order: 0, is_active: true });
    setEditingGallery(null);
  };

  // Image Management Functions
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length !== files.length) {
      toast({
        title: "Invalid files detected",
        description: "Only image files are allowed. Non-image files have been filtered out.",
        variant: "destructive",
      });
    }

    setSelectedFiles(prev => [...prev, ...imageFiles]);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length !== files.length) {
      toast({
        title: "Invalid files detected",
        description: "Only image files are allowed. Non-image files have been filtered out.",
        variant: "destructive",
      });
    }

    setSelectedFiles(prev => [...prev, ...imageFiles]);
  };

  const removeSelectedFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const clearAllFiles = () => {
    setSelectedFiles([]);
    setUploadProgress({});
  };

  const uploadImages = async (galleryId: number) => {
    if (selectedFiles.length === 0) return;

    setUploadingImages(true);
    setTotalFiles(selectedFiles.length);
    setUploadedCount(0);
    setUploadProgress({});

    try {
      // Create upload promises for simultaneous uploads
      const uploadPromises = selectedFiles.map(async (file, index) => {
        const fileKey = `${file.name}-${index}`;

        try {
          // Set initial progress
          setUploadProgress(prev => ({ ...prev, [fileKey]: 0 }));

          const formData = new FormData();
          formData.append('image', file);
          formData.append('gallery', galleryId.toString());
          formData.append('title', imageFormData.title || file.name.split('.')[0]);
          formData.append('description', imageFormData.description || `Image from ${file.name}`);
          formData.append('order', (imageFormData.order + index).toString());
          formData.append('is_active', imageFormData.is_active.toString());

          // Simulate progress (since we can't track actual upload progress with current API)
          const progressInterval = setInterval(() => {
            setUploadProgress(prev => {
              const currentProgress = prev[fileKey] || 0;
              if (currentProgress < 90) {
                return { ...prev, [fileKey]: currentProgress + 10 };
              }
              return prev;
            });
          }, 200);

          const response = await postReqMultipart('admin/gallery-images/', formData);

          clearInterval(progressInterval);

          // Check if the request was successful
          if (response && (response.status === "200" || response.status === "201" || response.data)) {
            setUploadProgress(prev => ({ ...prev, [fileKey]: 100 }));
            setUploadedCount(prev => prev + 1);
            return { success: true, file: file.name };
          } else {
            throw new Error(response?.message || 'Upload failed');
          }
        } catch (error) {
          setUploadProgress(prev => ({ ...prev, [fileKey]: -1 })); // -1 indicates error
          throw error;
        }
      });

      // Wait for all uploads to complete
      const results = await Promise.allSettled(uploadPromises);

      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      if (successful > 0) {
        toast({
          title: "Upload Complete",
          description: `${successful} image(s) uploaded successfully${failed > 0 ? `, ${failed} failed` : ''}`,
        });
      }

      if (failed > 0) {
        toast({
          title: "Some uploads failed",
          description: `${failed} image(s) failed to upload. Please try again.`,
          variant: "destructive",
        });
      }

      // Reset form and refresh galleries
      setSelectedFiles([]);
      setImageFormData({ title: '', description: '', order: 0, is_active: true });
      fetchGalleries();

    } catch (error) {
      
      toast({
        title: "Error",
        description: "Failed to upload images. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUploadingImages(false);
      setUploadProgress({});
      setUploadedCount(0);
      setTotalFiles(0);
    }
  };

  const handleImageEdit = async (imageId: number, imageData: Partial<GalleryImage>) => {
    try {
      await putReq(`admin/gallery-images/${imageId}/`, imageData);
      toast({
        title: "Success",
        description: "Image updated successfully",
      });
      fetchGalleries();
      setEditingImage(null);
    } catch (error) {
      
      toast({
        title: "Error",
        description: "Failed to update image",
        variant: "destructive",
      });
    }
  };

  const handleImageDelete = async (imageId: number) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      try {
        await deleteReq(`admin/gallery-images/${imageId}/`);
        toast({
          title: "Success",
          description: "Image deleted successfully",
        });
        fetchGalleries();
      } catch (error) {
        
        toast({
          title: "Error",
          description: "Failed to delete image",
          variant: "destructive",
        });
      }
    }
  };

  const openImageManager = (gallery: Gallery) => {
    setSelectedGallery(gallery);
    setIsImageDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-navy-dark">Gallery Management</h2>
          <p className="text-gray-medium">Manage your property galleries and images</p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={(open) => {
          setIsDialogOpen(open);
          if (!open) resetForm();
        }}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-navy-primary to-navy-light hover:from-navy-dark hover:to-navy-primary text-white shadow-lg hover:shadow-xl transition-all duration-300">
              <Plus className="h-4 w-4 mr-2" />
              Add Gallery
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingGallery ? 'Edit Gallery' : 'Create New Gallery'}
              </DialogTitle>
              <DialogDescription>
                {editingGallery ? 'Update gallery information' : 'Add a new gallery to showcase your properties'}
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>
              
              <div>
                <Label htmlFor="order">Display Order</Label>
                <Input
                  id="order"
                  type="number"
                  value={formData.order}
                  onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 0 })}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button type="submit" className="flex-1">
                  {editingGallery ? 'Update' : 'Create'} Gallery
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Galleries Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {galleries.map((gallery) => (
          <Card key={gallery.id} className="overflow-hidden">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{gallery.title}</CardTitle>
                  <CardDescription>{gallery.description}</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(gallery)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(gallery.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Badge variant={gallery.is_active ? "default" : "secondary"}>
                    {gallery.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                  <span className="text-sm text-gray-medium">Order: {gallery.order}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-gray-medium">
                  <Image className="h-4 w-4" />
                  <span>{gallery.images?.length || 0} images</span>
                </div>
                
                <Button
                  variant="outline"
                  className="w-full"
                  size="sm"
                  onClick={() => openImageManager(gallery)}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Manage Images
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {galleries.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Image className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No galleries yet</h3>
            <p className="text-gray-500 mb-4">Get started by creating your first gallery</p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Gallery
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Image Management Dialog */}
      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Manage Images - {selectedGallery?.title}</DialogTitle>
            <DialogDescription>
              Upload and manage images for this gallery
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload">Upload Images</TabsTrigger>
              <TabsTrigger value="manage">Manage Existing</TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-gray-medium/30 rounded-lg p-8 text-center hover:border-gold-primary hover:bg-gold-primary/5 transition-all duration-300"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <ImageIcon className="h-12 w-12 mx-auto text-gold-primary mb-4" />
                <p className="text-lg font-medium text-navy-dark mb-2">
                  Drag and drop images here, or click to select
                </p>
                <p className="text-sm text-gray-medium mb-4">
                  Supports: JPG, PNG, GIF (Max 10MB each)
                </p>
                <Button
                  variant="outline"
                  className="border-gold-primary text-gold-primary hover:bg-gold-primary hover:text-white transition-all duration-300"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Select Images
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>

              {/* Selected Files Preview */}
              {selectedFiles.length > 0 && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Selected Files ({selectedFiles.length})</h3>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearAllFiles}
                        disabled={uploadingImages}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Clear All
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadingImages}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add More
                      </Button>
                    </div>
                  </div>

                  {/* Upload Progress Summary */}
                  {uploadingImages && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-blue-900">
                          Uploading images... ({uploadedCount}/{totalFiles})
                        </span>
                        <span className="text-sm text-blue-700">
                          {Math.round((uploadedCount / totalFiles) * 100)}%
                        </span>
                      </div>
                      <div className="w-full bg-blue-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(uploadedCount / totalFiles) * 100}%` }}
                        />
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {selectedFiles.map((file, index) => {
                      const fileKey = `${file.name}-${index}`;
                      const progress = uploadProgress[fileKey];
                      const isUploading = uploadingImages && progress !== undefined;
                      const isError = progress === -1;
                      const isComplete = progress === 100;

                      return (
                        <div key={index} className="relative group">
                          <div className="relative overflow-hidden rounded-lg border">
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="w-full h-24 object-cover"
                            />

                            {/* Upload Progress Overlay */}
                            {isUploading && (
                              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                                {isError ? (
                                  <div className="text-red-400 text-center">
                                    <X className="h-6 w-6 mx-auto mb-1" />
                                    <span className="text-xs">Failed</span>
                                  </div>
                                ) : isComplete ? (
                                  <div className="text-green-400 text-center">
                                    <Check className="h-6 w-6 mx-auto mb-1" />
                                    <span className="text-xs">Done</span>
                                  </div>
                                ) : (
                                  <div className="text-white text-center">
                                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent mx-auto mb-1" />
                                    <span className="text-xs">{progress}%</span>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Remove Button */}
                            {!uploadingImages && (
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeSelectedFile(index)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            )}
                          </div>

                          <div className="mt-1">
                            <p className="text-xs text-center truncate" title={file.name}>
                              {file.name}
                            </p>
                            <p className="text-xs text-center text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(1)} MB
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Image Metadata Form */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-light rounded-lg border border-gray-medium/20">
                    <div>
                      <Label htmlFor="image-title">Default Title</Label>
                      <Input
                        id="image-title"
                        value={imageFormData.title}
                        onChange={(e) => setImageFormData({ ...imageFormData, title: e.target.value })}
                        placeholder="Leave empty to use filename"
                      />
                    </div>
                    <div>
                      <Label htmlFor="image-order">Display Order</Label>
                      <Input
                        id="image-order"
                        type="number"
                        value={imageFormData.order}
                        onChange={(e) => setImageFormData({ ...imageFormData, order: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="image-description">Default Description</Label>
                      <Textarea
                        id="image-description"
                        value={imageFormData.description}
                        onChange={(e) => setImageFormData({ ...imageFormData, description: e.target.value })}
                        rows={2}
                      />
                    </div>
                  </div>

                  <Button
                    onClick={() => selectedGallery && uploadImages(selectedGallery.id)}
                    disabled={uploadingImages}
                    className="w-full bg-gradient-to-r from-gold-primary to-gold-light hover:from-gold-dark hover:to-gold-primary text-white shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                  >
                    {uploadingImages ? 'Uploading...' : `Upload ${selectedFiles.length} Image(s)`}
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="manage" className="space-y-4">
              {selectedGallery?.images && selectedGallery.images.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {selectedGallery.images.map((image) => (
                    <Card key={image.id} className="overflow-hidden">
                      <div className="relative aspect-square">
                        <img
                          src={image.image}
                          alt={image.title}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute top-2 right-2 flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0 bg-white"
                            onClick={() => setEditingImage(image)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleImageDelete(image.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <CardContent className="p-3">
                        <h4 className="font-medium text-sm truncate text-navy-dark">{image.title || 'Untitled'}</h4>
                        <p className="text-xs text-gray-medium mt-1">Order: {image.order}</p>
                        <Badge variant={image.is_active ? "default" : "secondary"} className="mt-2">
                          {image.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ImageIcon className="h-12 w-12 mx-auto text-gold-primary mb-4" />
                  <p className="text-gray-medium">No images in this gallery yet.</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Image Edit Dialog */}
      {editingImage && (
        <Dialog open={!!editingImage} onOpenChange={() => setEditingImage(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Image</DialogTitle>
              <DialogDescription>
                Update image information
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="aspect-square w-32 mx-auto">
                <img
                  src={editingImage.image}
                  alt={editingImage.title}
                  className="w-full h-full object-cover rounded border"
                />
              </div>

              <div>
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  value={editingImage.title}
                  onChange={(e) => setEditingImage({ ...editingImage, title: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={editingImage.description}
                  onChange={(e) => setEditingImage({ ...editingImage, description: e.target.value })}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="edit-order">Display Order</Label>
                <Input
                  id="edit-order"
                  type="number"
                  value={editingImage.order}
                  onChange={(e) => setEditingImage({ ...editingImage, order: parseInt(e.target.value) || 0 })}
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-active"
                  checked={editingImage.is_active}
                  onChange={(e) => setEditingImage({ ...editingImage, is_active: e.target.checked })}
                />
                <Label htmlFor="edit-active">Active</Label>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={() => handleImageEdit(editingImage.id, editingImage)}
                  className="flex-1"
                >
                  Update Image
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setEditingImage(null)}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
