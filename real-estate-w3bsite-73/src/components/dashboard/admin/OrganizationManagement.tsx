import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Save, Upload } from 'lucide-react';
import { useEffect } from 'react';
import { getReq, putReq, putReqMultipart } from '@/apiService';
import { Organization } from '@/types';

export const OrganizationManagement = () => {
  const [orgData, setOrgData] = useState<Organization>({
    name: '',
    email: '',
    phone: '',
    address: '',
    description: '',
    logo: '',
    whatsapp: '',
    facebook: '',
    instagram: '',
    linkedin: '',
    twitter: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);

  useEffect(() => {
    getReq('admin/organization/')
      .then((data) => {
        if (data && typeof data === 'object') {
          setOrgData(data);
        } else if (data === null) {
          console.error('OrganizationManagement: API call returned null');
          setOrgData({});
        } else {
          setOrgData({});
        }
      })
      .catch((err) => {
        console.error('OrganizationManagement: Error fetching data:', err);
        setOrgData({});
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const handleSave = async () => {
    setSaving(true);
    try {
      if (logoFile) {
        // Use multipart if there's a file to upload
        const formData = new FormData();
        formData.append('name', orgData.name || '');
        formData.append('email', orgData.email || '');
        formData.append('phone', orgData.phone || '');
        formData.append('address', orgData.address || '');
        formData.append('description', orgData.description || '');
        formData.append('whatsapp', orgData.whatsapp || '');
        formData.append('facebook', orgData.facebook || '');
        formData.append('instagram', orgData.instagram || '');
        formData.append('linkedin', orgData.linkedin || '');
        formData.append('twitter', orgData.twitter || '');
        formData.append('logo', logoFile);

        const result = await putReqMultipart('admin/organization/', formData);
        if (result.status === "200") {
          console.log('Organization updated successfully');
        } else {
          console.error('OrganizationManagement: Error saving data:', result.message);
        }
      } else {
        // Use JSON if no file upload
        const jsonData = {
          name: orgData.name || '',
          email: orgData.email || '',
          phone: orgData.phone || '',
          address: orgData.address || '',
          description: orgData.description || '',
          whatsapp: orgData.whatsapp || '',
          facebook: orgData.facebook || '',
          instagram: orgData.instagram || '',
          linkedin: orgData.linkedin || '',
          twitter: orgData.twitter || ''
        };

        const result = await putReq('admin/organization/', jsonData);
        if (result.status === "200") {
          console.log('Organization updated successfully');
        } else {
          console.error('OrganizationManagement: Error saving data:', result.message);
        }
      }
    } catch (error) {
      console.error('OrganizationManagement: Error saving data:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-navy-primary"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Organization Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="org-name">Company Name</Label>
            <Input id="org-name" value={orgData.name} onChange={(e) => setOrgData({...orgData, name: e.target.value})} />
          </div>
            <div>
              <Label htmlFor="org-email">Email</Label>
              <Input id="org-email" value={orgData.email} onChange={(e) => setOrgData({...orgData, email: e.target.value})} />
            </div>
            <div>
              <Label htmlFor="org-phone">Phone</Label>
              <Input id="org-phone" value={orgData.phone} onChange={(e) => setOrgData({...orgData, phone: e.target.value})} />
            </div>

          </div>
          <div>
            <Label htmlFor="org-address">Address</Label>
            <Textarea id="org-address" value={orgData.address} onChange={(e) => setOrgData({...orgData, address: e.target.value})} />
          </div>
          <div>
            <Label htmlFor="org-description">Description</Label>
            <Textarea id="org-description" value={orgData.description} onChange={(e) => setOrgData({...orgData, description: e.target.value})} rows={3} />
          </div>
          <div>
            <Label htmlFor="org-logo">Logo</Label>
            <div className="space-y-2">
              <Input
                id="org-logo"
                type="file"
                accept="image/*"
                onChange={(e) => setLogoFile(e.target.files?.[0] || null)}
              />
              {orgData.logo && (
                <p className="text-sm text-muted-foreground">
                  Current: {typeof orgData.logo === 'string' ? orgData.logo.split('/').pop() : 'Logo uploaded'}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="org-whatsapp">WhatsApp</Label>
              <Input id="org-whatsapp" value={orgData.whatsapp} onChange={(e) => setOrgData({...orgData, whatsapp: e.target.value})} />
            </div>
            <div>
              <Label htmlFor="org-facebook">Facebook</Label>
              <Input id="org-facebook" value={orgData.facebook} onChange={(e) => setOrgData({...orgData, facebook: e.target.value})} />
            </div>
            <div>
              <Label htmlFor="org-instagram">Instagram</Label>
              <Input id="org-instagram" value={orgData.instagram} onChange={(e) => setOrgData({...orgData, instagram: e.target.value})} />
            </div>
            <div>
              <Label htmlFor="org-linkedin">LinkedIn</Label>
              <Input id="org-linkedin" value={orgData.linkedin} onChange={(e) => setOrgData({...orgData, linkedin: e.target.value})} />
            </div>
            <div>
              <Label htmlFor="org-twitter">Twitter</Label>
              <Input id="org-twitter" value={orgData.twitter} onChange={(e) => setOrgData({...orgData, twitter: e.target.value})} />
            </div>
        </div>
        <Button onClick={handleSave} disabled={saving}>
          <Save className="mr-2 h-4 w-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </CardContent>
    </Card>
  );
};