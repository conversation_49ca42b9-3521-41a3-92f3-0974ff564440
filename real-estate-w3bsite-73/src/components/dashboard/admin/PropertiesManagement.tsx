import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DistrictCombobox } from '@/components/ui/district-combobox';
import { Plus, Edit, Trash2, Save, Upload, X, Star, Check, Eye } from 'lucide-react';
import { Property, PropertyType } from '@/types';
import { getReq, postReq, postReqMultipart, putReq, putReqMultipart, patchReq, deleteReq } from '@/apiService';
import { validateLandArea, formatLandArea } from '@/utils/propertyUtils';

export const PropertiesManagement = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [propertyTypes, setPropertyTypes] = useState<PropertyType[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [existingImages, setExistingImages] = useState<any[]>([]);
  const [uploadingImages, setUploadingImages] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  const [primaryImageIndex, setPrimaryImageIndex] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [propertiesData, typesData] = await Promise.all([
          getReq('admin/properties/'),
          getReq('admin/property-types/')
        ]);

        setProperties(Array.isArray(propertiesData) ? propertiesData : []);
        setPropertyTypes(Array.isArray(typesData) ? typesData : []);
      } catch (err) {
        
        setError('Failed to load properties data. Please try again.');
        setProperties([]);
        setPropertyTypes([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Image Management Functions
  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length !== files.length) {
      alert('Only image files are allowed. Non-image files have been filtered out.');
    }

    setSelectedImages(prev => [...prev, ...imageFiles]);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length !== files.length) {
      alert('Only image files are allowed. Non-image files have been filtered out.');
    }

    setSelectedImages(prev => [...prev, ...imageFiles]);
  };

  const removeSelectedImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    if (primaryImageIndex >= index && primaryImageIndex > 0) {
      setPrimaryImageIndex(prev => prev - 1);
    }
  };

  const clearAllImages = () => {
    setSelectedImages([]);
    setUploadProgress({});
    setPrimaryImageIndex(0);
  };

  const setPrimaryImage = (index: number) => {
    setPrimaryImageIndex(index);
  };



  const handlePropertyDelete = async (propertyId: string) => {
    if (confirm('Are you sure you want to delete this property?')) {
      setSaving(true);
      try {
        const response = await deleteReq(`admin/properties/${propertyId}/`);

        // Check if the request was successful
        if (response && (response.status === "200" || response.status === "204")) {
          setProperties(properties.filter(p => p.id !== propertyId));
          alert('Property deleted successfully!');
        } else {
          // Handle API error response
          const errorMessage = response?.message || 'Failed to delete property. Please try again.';
          alert(errorMessage);
        }
      } catch (error) {
        
        alert('An error occurred while deleting the property. Please try again.');
      } finally {
        setSaving(false);
      }
    }
  };

  const handlePropertyEdit = (property: Property) => {
    setEditingItem(property.id);
    setFormData(property);
    setSelectedImages([]);
    setExistingImages(property.images || []);
    setPrimaryImageIndex(0);
    setUploadProgress({});
    setShowAddForm(true);
  };

  const handlePropertyAdd = () => {
    setEditingItem(null);
    setFormData({
      title: '',
      description: '',
      price: '',
      location: '',
      address: '',
      bedrooms: '',
      bathrooms: '',
      area: '',
      area_unit: 'aana',
      property_purpose: 'land',
      land_ropani: '',
      land_aana: '',
      land_paisa: '',
      land_daam: '',
      google_maps_embed_url: '',
      latitude: '',
      longitude: '',
      is_featured: false,
      is_active: true,
      property_type: propertyTypes[0]?.id || ''
    });
    setImageFile(null);
    setSelectedImages([]);
    setExistingImages([]);
    setPrimaryImageIndex(0);
    setUploadProgress({});
    setShowAddForm(true);
  };

  const handlePropertyImageDelete = async (imageId: number) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      try {
        const response = await deleteReq(`admin/property-images/${imageId}/`);

        if (response && (response.status === "200" || response.status === "204")) {
          // Remove the image from existing images
          setExistingImages(prev => prev.filter(img => img.id !== imageId));
          alert('Image deleted successfully!');
        } else {
          const errorMessage = response?.message || 'Failed to delete image. Please try again.';
          alert(errorMessage);
        }
      } catch (error) {
        console.error('Error deleting image:', error);
        alert('An error occurred while deleting the image. Please try again.');
      }
    }
  };

  const handleSetPrimaryImage = async (imageId: number) => {
    try {
      const response = await patchReq(`admin/property-images/${imageId}/`, { is_primary: true });

      if (response && (response.status === "200" || response.data)) {
        // Update existing images to reflect the new primary image
        setExistingImages(prev => prev.map(img => ({
          ...img,
          is_primary: img.id === imageId
        })));
        alert('Primary image updated successfully!');
      } else {
        const errorMessage = response?.message || 'Failed to update primary image. Please try again.';
        alert(errorMessage);
      }
    } catch (error) {
      console.error('Error updating primary image:', error);
      alert('An error occurred while updating the primary image. Please try again.');
    }
  };

  const uploadPropertyImages = async (propertyId: string) => {
    if (selectedImages.length === 0) return;

    setUploadingImages(true);
    try {
      // Get the starting order number based on existing images
      const startingOrder = existingImages.length;

      // Upload images simultaneously
      const uploadPromises = selectedImages.map(async (file, index) => {
        const fileKey = `${file.name}-${index}`;

        try {
          setUploadProgress(prev => ({ ...prev, [fileKey]: 0 }));

          const formData = new FormData();
          formData.append('image', file);
          formData.append('property', propertyId);

          // Set primary image logic: if no existing images and this is the first selected image, make it primary
          // Or if user explicitly selected this as primary
          const shouldBePrimary = (existingImages.length === 0 && index === 0) || (index === primaryImageIndex);
          formData.append('is_primary', shouldBePrimary.toString());

          // Set order starting from the next available order
          formData.append('order', (startingOrder + index).toString());

          // Simulate progress
          const progressInterval = setInterval(() => {
            setUploadProgress(prev => {
              const currentProgress = prev[fileKey] || 0;
              if (currentProgress < 90) {
                return { ...prev, [fileKey]: currentProgress + 10 };
              }
              return prev;
            });
          }, 200);

          const response = await postReqMultipart('admin/property-images/', formData);

          clearInterval(progressInterval);

          if (response && (response.status === "200" || response.status === "201" || response.data)) {
            setUploadProgress(prev => ({ ...prev, [fileKey]: 100 }));
            return { success: true, file: file.name };
          } else {
            throw new Error(response?.message || 'Upload failed');
          }
        } catch (error) {
          setUploadProgress(prev => ({ ...prev, [fileKey]: -1 }));
          throw error;
        }
      });

      const results = await Promise.allSettled(uploadPromises);
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.filter(result => result.status === 'rejected').length;

      if (successful > 0) {
        alert(`${successful} image(s) uploaded successfully${failed > 0 ? `, ${failed} failed` : ''}`);
      }

      if (failed > 0) {
        alert(`${failed} image(s) failed to upload. Please try again.`);
      }

    } catch (error) {
      
      alert('Failed to upload images. Please try again.');
    } finally {
      setUploadingImages(false);
      setUploadProgress({});
    }
  };

  const handlePropertySave = async () => {
    // Validate required fields
    if (!formData.title?.trim() || !formData.description?.trim() || !formData.property_type || !formData.location?.trim() || !formData.address?.trim()) {
      alert('Please fill in all required fields (Title, Description, Property Type, Location, Address)');
      return;
    }

    // Validate property type is a valid number
    if (isNaN(parseInt(formData.property_type))) {
      alert('Please select a valid property type');
      return;
    }

    // Validate bathrooms is a valid number (required field)
    if (!formData.bathrooms || isNaN(parseInt(formData.bathrooms))) {
      alert('Please enter a valid number of bathrooms');
      return;
    }

    // Validate land area if provided
    const landAreaValidation = validateLandArea(
      formData.land_ropani ? parseInt(formData.land_ropani) : undefined,
      formData.land_aana ? parseInt(formData.land_aana) : undefined,
      formData.land_paisa ? parseInt(formData.land_paisa) : undefined,
      formData.land_daam ? parseInt(formData.land_daam) : undefined
    );

    if (!landAreaValidation.isValid) {
      alert(`Land area validation errors:\n${landAreaValidation.errors.join('\n')}`);
      return;
    }

    setSaving(true);
    try {
      const propertyDataToSend = {
        title: formData.title?.trim() || '',
        description: formData.description?.trim() || '',
        property_type: formData.property_type && !isNaN(parseInt(formData.property_type)) ? parseInt(formData.property_type) : null,
        price: formData.price && !isNaN(parseFloat(formData.price)) ? parseFloat(formData.price) : null,
        bedrooms: formData.property_purpose === 'rent' && formData.bedrooms && !isNaN(parseInt(formData.bedrooms)) ? parseInt(formData.bedrooms) : null,
        bathrooms: formData.bathrooms && !isNaN(parseInt(formData.bathrooms)) ? parseInt(formData.bathrooms) : 0,
        area: formData.area && !isNaN(parseFloat(formData.area)) ? parseFloat(formData.area) : null,
        area_unit: formData.area_unit || 'aana',
        property_purpose: formData.property_purpose || 'land',
        land_ropani: formData.land_ropani && !isNaN(parseInt(formData.land_ropani)) ? parseInt(formData.land_ropani) : null,
        land_aana: formData.land_aana && !isNaN(parseInt(formData.land_aana)) ? parseInt(formData.land_aana) : null,
        land_paisa: formData.land_paisa && !isNaN(parseInt(formData.land_paisa)) ? parseInt(formData.land_paisa) : null,
        land_daam: formData.land_daam && !isNaN(parseInt(formData.land_daam)) ? parseInt(formData.land_daam) : null,
        google_maps_embed_url: formData.google_maps_embed_url?.trim() || null,
        location: formData.location?.trim() || '',
        address: formData.address?.trim() || '',
        latitude: formData.latitude && !isNaN(parseFloat(formData.latitude)) ? parseFloat(formData.latitude) : null,
        longitude: formData.longitude && !isNaN(parseFloat(formData.longitude)) ? parseFloat(formData.longitude) : null,
        is_featured: formData.is_featured || false,
        is_active: formData.is_active !== undefined ? formData.is_active : true
      };

      let propertyData: any;
      if (editingItem) {
        propertyData = await putReq(`admin/properties/${editingItem}/`, propertyDataToSend);
      } else {
        propertyData = await postReq('admin/properties/', propertyDataToSend);
      }

      // Check if the request was successful
      if (propertyData && (propertyData.status === "200" || propertyData.status === "201" || propertyData.data)) {
        const propertyId = editingItem || propertyData.data?.id || propertyData.id;

        // Upload images if any are selected
        if (selectedImages.length > 0 && propertyId) {
          await uploadPropertyImages(propertyId.toString());
        }

        // Refresh properties list
        const updatedProperties = await getReq('admin/properties/');
        if (Array.isArray(updatedProperties)) {
          setProperties(updatedProperties);

          // Update existing images if editing
          if (editingItem) {
            const updatedProperty = updatedProperties.find(p => p.id === editingItem);
            if (updatedProperty) {
              setExistingImages(updatedProperty.images || []);
            }
          }
        }

        // Reset form and close dialog
        setShowAddForm(false);
        setEditingItem(null);
        setFormData({});
        setImageFile(null);
        setSelectedImages([]);
        setExistingImages([]);
        setPrimaryImageIndex(0);

        // Show success message
        alert(editingItem ? 'Property updated successfully!' : 'Property created successfully!');
      } else {
        // Handle API error response
        const errorMessage = propertyData?.message || 'Failed to save property. Please try again.';
        alert(errorMessage);
      }
    } catch (error) {
      
      alert('An error occurred while saving the property. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gold-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-navy-primary text-white rounded hover:bg-navy-dark"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-2xl font-bold">Properties Management</h2>
        <Button onClick={handlePropertyAdd} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Property
        </Button>
      </div>

      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingItem ? 'Edit Property' : 'Add New Property'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="property-title">Title *</Label>
                <Input
                  id="property-title"
                  value={formData.title || ''}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  placeholder="Enter property title"
                />
              </div>
              <div>
                <Label htmlFor="property-price">Price *</Label>
                <Input
                  id="property-price"
                  type="number"
                  value={formData.price || ''}
                  onChange={(e) => setFormData({...formData, price: e.target.value})}
                  placeholder="Enter price"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="property-description">Description *</Label>
              <Textarea
                id="property-description"
                value={formData.description || ''}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Enter property description"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="property-location">Location *</Label>
                <DistrictCombobox
                  value={formData.location || ''}
                  onValueChange={(value) => setFormData({...formData, location: value})}
                  placeholder="Select or type district..."
                  className="w-full"
                />
              </div>
              <div>
                <Label htmlFor="property-type">Property Type *</Label>
                <Select
                  value={formData.property_type || ''}
                  onValueChange={(value) => setFormData({...formData, property_type: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select property type" />
                  </SelectTrigger>
                  <SelectContent>
                    {propertyTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="property-purpose">Property Purpose *</Label>
                <Select
                  value={formData.property_purpose || 'land'}
                  onValueChange={(value) => setFormData({...formData, property_purpose: value})}
                >
                  <SelectTrigger id="property-purpose">
                    <SelectValue placeholder="Select purpose" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="land">Land (जग्गा)</SelectItem>
                    <SelectItem value="rent">Rent (भाडा)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Conditional Bedrooms field - only show for rent properties */}
              {formData.property_purpose === 'rent' && (
                <div>
                  <Label htmlFor="property-bedrooms">Bedrooms</Label>
                  <Input
                    id="property-bedrooms"
                    type="number"
                    value={formData.bedrooms || ''}
                    onChange={(e) => setFormData({...formData, bedrooms: e.target.value})}
                    placeholder="Number of bedrooms"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="property-bathrooms">Bathrooms</Label>
                <Input
                  id="property-bathrooms"
                  type="number"
                  value={formData.bathrooms || ''}
                  onChange={(e) => setFormData({...formData, bathrooms: e.target.value})}
                  placeholder="Number of bathrooms"
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="property-area">Area</Label>
                  <Input
                    id="property-area"
                    type="number"
                    value={formData.area || ''}
                    onChange={(e) => setFormData({...formData, area: e.target.value})}
                    placeholder="Area value"
                  />
                </div>
                <div>
                  <Label htmlFor="property-area-unit">Unit</Label>
                  <Select
                    value={formData.area_unit || 'aana'}
                    onValueChange={(value) => setFormData({...formData, area_unit: value})}
                  >
                    <SelectTrigger id="property-area-unit">
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aana">Aana (आना)</SelectItem>
                      <SelectItem value="ropani">Ropani (रोपनी)</SelectItem>
                      <SelectItem value="dhur">Dhur (धुर)</SelectItem>
                      <SelectItem value="bigha">Bigha (बिघा)</SelectItem>
                      <SelectItem value="kattha">Kattha (कट्ठा)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Sophisticated Land Area System (Ropani-Aana-Paisa-Daam) */}
            <div className="space-y-4">
              <Label className="text-base font-semibold">Land Area (Ropani-Aana-Paisa-Daam Format)</Label>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="land-ropani">Ropani (रोपनी)</Label>
                  <Input
                    id="land-ropani"
                    type="number"
                    min="0"
                    value={formData.land_ropani || ''}
                    onChange={(e) => setFormData({...formData, land_ropani: e.target.value})}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="land-aana">Aana (आना)</Label>
                  <Input
                    id="land-aana"
                    type="number"
                    min="0"
                    max="15"
                    value={formData.land_aana || ''}
                    onChange={(e) => setFormData({...formData, land_aana: e.target.value})}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="land-paisa">Paisa (पैसा)</Label>
                  <Input
                    id="land-paisa"
                    type="number"
                    min="0"
                    max="3"
                    value={formData.land_paisa || ''}
                    onChange={(e) => setFormData({...formData, land_paisa: e.target.value})}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="land-daam">Daam (दाम)</Label>
                  <Input
                    id="land-daam"
                    type="number"
                    min="0"
                    max="3"
                    value={formData.land_daam || ''}
                    onChange={(e) => setFormData({...formData, land_daam: e.target.value})}
                    placeholder="0"
                  />
                </div>
              </div>
              <p className="text-sm text-gray-500">
                Format: {formatLandArea(
                  formData.land_ropani ? parseInt(formData.land_ropani) : undefined,
                  formData.land_aana ? parseInt(formData.land_aana) : undefined,
                  formData.land_paisa ? parseInt(formData.land_paisa) : undefined,
                  formData.land_daam ? parseInt(formData.land_daam) : undefined
                )}
              </p>
            </div>

            {/* Google Maps Embed Section */}
            <div className="space-y-4">
              <Label htmlFor="google-maps-embed">Google Maps Embed</Label>
              <Textarea
                id="google-maps-embed"
                value={formData.google_maps_embed_url || ''}
                onChange={(e) => setFormData({...formData, google_maps_embed_url: e.target.value})}
                placeholder="Paste your Google Maps embed iframe code here..."
                rows={3}
              />
              <p className="text-sm text-gray-500">
                Paste the complete iframe code from Google Maps. The system will automatically extract the necessary URL.
              </p>
              {formData.google_maps_embed_url && (
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-700 mb-2">Preview:</p>
                  <div className="text-xs text-gray-600 break-all">
                    {formData.google_maps_embed_url.substring(0, 100)}...
                  </div>
                </div>
              )}
            </div>

            {/* Multiple Image Upload Section */}
            <div className="space-y-4">
              <Label>Property Images</Label>

              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gold-primary hover:bg-gold-primary/5 transition-all duration-300 cursor-pointer"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-8 w-8 mx-auto text-gold-primary mb-2" />
                <p className="text-sm font-medium text-gray-700 mb-1">
                  Drag and drop images here, or click to select
                </p>
                <p className="text-xs text-gray-500">
                  Supports: JPG, PNG, GIF (Max 10MB each)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageSelect}
                  className="hidden"
                />
              </div>

              {/* Selected Images Preview */}
              {selectedImages.length > 0 && (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium">Selected Images ({selectedImages.length})</h4>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={clearAllImages}
                        disabled={uploadingImages}
                      >
                        <X className="h-3 w-3 mr-1" />
                        Clear All
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadingImages}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Add More
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {selectedImages.map((file, index) => {
                      const fileKey = `${file.name}-${index}`;
                      const progress = uploadProgress[fileKey];
                      const isUploading = uploadingImages && progress !== undefined;
                      const isError = progress === -1;
                      const isComplete = progress === 100;
                      const isPrimary = index === primaryImageIndex;

                      return (
                        <div key={index} className="relative group">
                          <div className={`relative overflow-hidden rounded-lg border-2 ${isPrimary ? 'border-gold-primary' : 'border-gray-200'}`}>
                            <img
                              src={URL.createObjectURL(file)}
                              alt={file.name}
                              className="w-full h-20 object-cover"
                            />

                            {/* Primary Badge */}
                            {isPrimary && (
                              <div className="absolute top-1 left-1 bg-gold-primary text-white text-xs px-1 py-0.5 rounded">
                                Primary
                              </div>
                            )}

                            {/* Upload Progress Overlay */}
                            {isUploading && (
                              <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                                {isError ? (
                                  <div className="text-red-400 text-center">
                                    <X className="h-4 w-4 mx-auto mb-1" />
                                    <span className="text-xs">Failed</span>
                                  </div>
                                ) : isComplete ? (
                                  <div className="text-green-400 text-center">
                                    <Check className="h-4 w-4 mx-auto mb-1" />
                                    <span className="text-xs">Done</span>
                                  </div>
                                ) : (
                                  <div className="text-white text-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mx-auto mb-1" />
                                    <span className="text-xs">{progress}%</span>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Action Buttons */}
                            {!uploadingImages && (
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                                <div className="flex gap-1">
                                  {!isPrimary && (
                                    <Button
                                      type="button"
                                      variant="secondary"
                                      size="sm"
                                      className="h-6 w-6 p-0"
                                      onClick={() => setPrimaryImage(index)}
                                      title="Set as primary"
                                    >
                                      <Star className="h-3 w-3" />
                                    </Button>
                                  )}
                                  <Button
                                    type="button"
                                    variant="destructive"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => removeSelectedImage(index)}
                                    title="Remove image"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="mt-1">
                            <p className="text-xs text-center truncate" title={file.name}>
                              {file.name}
                            </p>
                            <p className="text-xs text-center text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(1)} MB
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Existing Images Section */}
              {existingImages.length > 0 && (
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium">Existing Images ({existingImages.length})</h4>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {existingImages.map((image) => {
                      const isPrimary = image.is_primary;

                      return (
                        <div key={image.id} className="relative group">
                          <div className={`relative overflow-hidden rounded-lg border-2 ${isPrimary ? 'border-gold-primary' : 'border-gray-200'}`}>
                            <img
                              src={image.image}
                              alt={`Property image ${image.order + 1}`}
                              className="w-full h-20 object-cover"
                            />

                            {/* Primary Badge */}
                            {isPrimary && (
                              <div className="absolute top-1 left-1 bg-gold-primary text-white text-xs px-1 py-0.5 rounded">
                                Primary
                              </div>
                            )}

                            {/* Action Buttons */}
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="flex gap-1">
                                {!isPrimary && (
                                  <Button
                                    type="button"
                                    variant="secondary"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => handleSetPrimaryImage(image.id)}
                                    title="Set as primary"
                                  >
                                    <Star className="h-3 w-3" />
                                  </Button>
                                )}
                                <Button
                                  type="button"
                                  variant="secondary"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() => window.open(image.image, '_blank')}
                                  title="View full image"
                                >
                                  <Eye className="h-3 w-3" />
                                </Button>
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() => handlePropertyImageDelete(image.id)}
                                  title="Delete image"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>

                          <div className="mt-1">
                            <p className="text-xs text-center text-gray-500">
                              Order: {image.order}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
            <div>
              <Label htmlFor="property-address">Address *</Label>
              <Textarea
                id="property-address"
                value={formData.address || ''}
                onChange={(e) => setFormData({...formData, address: e.target.value})}
                placeholder="Enter full address"
                rows={2}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="property-latitude">Latitude (Optional)</Label>
                <Input
                  id="property-latitude"
                  type="number"
                  step="any"
                  value={formData.latitude || ''}
                  onChange={(e) => setFormData({...formData, latitude: e.target.value})}
                  placeholder="e.g., 40.7128"
                />
              </div>
              <div>
                <Label htmlFor="property-longitude">Longitude (Optional)</Label>
                <Input
                  id="property-longitude"
                  type="number"
                  step="any"
                  value={formData.longitude || ''}
                  onChange={(e) => setFormData({...formData, longitude: e.target.value})}
                  placeholder="e.g., -74.0060"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="property-featured"
                  checked={formData.is_featured || false}
                  onCheckedChange={(checked) => setFormData({...formData, is_featured: checked})}
                />
                <Label htmlFor="property-featured">Featured Property</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="property-active"
                  checked={formData.is_active !== undefined ? formData.is_active : true}
                  onCheckedChange={(checked) => setFormData({...formData, is_active: checked})}
                />
                <Label htmlFor="property-active">Active</Label>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handlePropertySave} disabled={saving} className="flex-1">
                <Save className="mr-2 h-4 w-4" />
                {saving ? 'Saving...' : (editingItem ? 'Update Property' : 'Save Property')}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowAddForm(false);
                  setEditingItem(null);
                  setFormData({});
                  setImageFile(null);
                  setSelectedImages([]);
                  setExistingImages([]);
                  setPrimaryImageIndex(0);
                  setUploadProgress({});
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Properties List</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead className="hidden md:table-cell">Location</TableHead>
                  <TableHead className="hidden lg:table-cell">Price</TableHead>
                  <TableHead className="hidden md:table-cell">Purpose</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="hidden lg:table-cell">Featured</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {properties.map((property) => (
                  <TableRow key={property.id}>
                    <TableCell className="font-medium">{property.title}</TableCell>
                    <TableCell className="hidden md:table-cell">{property.location}</TableCell>
                    <TableCell className="hidden lg:table-cell">Rs.{property.price?.toLocaleString() || 'N/A'}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      <Badge variant="outline">
                        {property.purpose_display || property.property_purpose || 'Land'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={property.is_active ? "default" : "secondary"}>
                        {property.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      {property.is_featured && <Badge variant="outline">Featured</Badge>}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePropertyEdit(property)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePropertyDelete(property.id)}
                          disabled={saving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};