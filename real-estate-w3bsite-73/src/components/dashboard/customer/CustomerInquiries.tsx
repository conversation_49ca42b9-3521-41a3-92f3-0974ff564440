import React, { useState, useEffect } from 'react';
import { getReq } from '@/apiService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useAuth } from '@/contexts/AuthContext';
import { Property, Agent } from '@/types';

// Backend PropertyInquiry response structure
interface PropertyInquiryResponse {
  id: string;
  customer: string;
  property: string;
  agent?: string;
  message: string;
  status: 'pending' | 'responded' | 'closed';
  created_at: string;
  customer_details: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  property_details: Property;
  agent_details?: Agent;
}

export const CustomerInquiries = () => {
  const { user } = useAuth();
  const [inquiries, setInquiries] = useState<PropertyInquiryResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInquiries = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const data = await getReq('customer/inquiries/');
        if (Array.isArray(data)) {
          setInquiries(data);
        } else {
          setInquiries([]);
        }
      } catch (err) {
       
        setError('Failed to load inquiries. Please try again.');
        setInquiries([]);
      } finally {
        setLoading(false);
      }
    };

    fetchInquiries();
  }, [user?.id]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'destructive';
      case 'responded':
        return 'default';
      case 'closed':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>My Inquiries</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>My Inquiries ({inquiries.length})</CardTitle>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        ) : inquiries.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No inquiries yet.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Browse properties and send inquiries to agents to see them here.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="hidden md:table-cell">Date</TableHead>
                  <TableHead className="hidden lg:table-cell">Agent</TableHead>
                  <TableHead className="hidden xl:table-cell">Message</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inquiries.map((inquiry) => (
                  <TableRow key={inquiry.id}>
                    <TableCell className="font-medium">
                      <div className="max-w-[200px]">
                        <div className="truncate font-semibold">{inquiry.property_details?.title}</div>
                        <div className="text-sm text-muted-foreground truncate">
                          {inquiry.property_details?.location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusVariant(inquiry.status)}>
                        {inquiry.status.charAt(0).toUpperCase() + inquiry.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {formatDate(inquiry.created_at)}
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="max-w-[150px] truncate">
                        {inquiry.agent_details?.name || 'Not assigned'}
                      </div>
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      <div className="max-w-xs truncate" title={inquiry.message}>
                        {inquiry.message}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">View</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};