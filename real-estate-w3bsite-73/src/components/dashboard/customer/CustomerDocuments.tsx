import React, { useState, useEffect } from 'react';
import { getReq } from '@/apiService';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const CustomerDocuments = () => {
  const { user } = useAuth();
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDocuments = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const data = await getReq('customer/documents/');
        setDocuments(Array.isArray(data) ? data : []);
      } catch (err) {
      
        // Fallback to mock data if API fails
        setDocuments([
          {
            id: '1',
            title: 'Property Brochure - Luxury Downtown Apartment',
            downloaded_at: 'Jan 15, 2024'
          },
          {
            id: '2',
            title: 'Financing Options Guide',
            downloaded_at: 'Jan 10, 2024'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [user?.id]);
  return (
    <Card>
      <CardHeader>
        <CardTitle>Documents</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {documents.map((doc: any) => (
              <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">{doc.title}</p>
                  <p className="text-sm text-muted-foreground">Downloaded on {doc.downloaded_at}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(`${import.meta.env.VITE_API_URL}/api/customer/documents/${doc.id}/download/`, '_blank')}
                >
                  Download
                </Button>
              </div>
            ))}
            {documents.length === 0 && (
              <p className="text-muted-foreground">No documents available yet.</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};