import React, { useState, useEffect } from 'react';
import { getReq, postReq } from '@/apiService';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export const PropertyAlerts = () => {
  const { user } = useAuth();
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [alertData, setAlertData] = useState({
    price_range: '',
    location: '',
    property_type: ''
  });

  useEffect(() => {
    getReq('customer/alerts/')
      .then((data) => {
        if (Array.isArray(data)) {
          setAlerts(data);
        } else if (data && data.data && Array.isArray(data.data)) {
          setAlerts(data.data);
        } else if (data === null) {
          
          setAlerts([{
            id: '1',
            description: 'Apartments in Downtown, $400k-$800k',
            customer_id: user?.id
          }]);
        } else {
          
          setAlerts([{
            id: '1',
            description: 'Apartments in Downtown, $400k-$800k',
            customer_id: user?.id
          }]);
        }
      })
      .catch((err) => {
        
        setAlerts([{
          id: '1',
          description: 'Apartments in Downtown, $400k-$800k',
          customer_id: user?.id
        }]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [user?.id]);

  const handleSaveAlert = async () => {
    setSaving(true);
    try {
      const result = await postReq('customer/alerts/', {
        ...alertData,
        customer_id: user?.id
      });
      if (result.status === "200" || result.status === "201") {
        // Refresh alerts
        const data = await getReq(`customer/alerts/`);
        if (Array.isArray(data)) {
          setAlerts(data);
        }
      }
    } catch (error) {
      
    } finally {
      setSaving(false);
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Property Alerts</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Price Range</Label>
                <Input 
                  value={alertData.price_range}
                  onChange={(e) => setAlertData({...alertData, price_range: e.target.value})}
                  placeholder="e.g., 400,000 - 800,000" 
                />
              </div>
              <div>
                <Label>Location</Label>
                <Input 
                  value={alertData.location}
                  onChange={(e) => setAlertData({...alertData, location: e.target.value})}
                  placeholder="e.g., Downtown, Brooklyn" 
                />
              </div>
              <div>
                <Label>Property Type</Label>
                <Input 
                  value={alertData.property_type}
                  onChange={(e) => setAlertData({...alertData, property_type: e.target.value})}
                  placeholder="e.g., Apartment, House" 
                />
              </div>
            </div>
            <Button 
              variant="elegant" 
              className="w-full sm:w-auto"
              onClick={handleSaveAlert}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Alert Preferences'}
            </Button>
          
            <div className="mt-6">
              <h4 className="font-semibold mb-3">Active Alerts</h4>
              <div className="space-y-2">
                {alerts.map((alert: any) => (
                  <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <span>{alert.description}</span>
                    <Button variant="outline" size="sm">Remove</Button>
                  </div>
                ))}
                {alerts.length === 0 && (
                  <p className="text-muted-foreground">No active alerts yet.</p>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};