import React, { useState, useEffect } from 'react';
import { getReq, postReq, putReq, postReqMultipart } from '@/apiService';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export const ProfileManagement = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    first_name: user?.name?.split(' ')[0] || '',
    last_name: user?.name?.split(' ').slice(1).join(' ') || '',
    email: user?.email || '',
    phone_number: user?.phone || ''
  });
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  useEffect(() => {
    // Use the current user data from auth context
    if (user) {
      setProfileData({
        username: user.username || '',
        first_name: user.name?.split(' ')[0] || '',
        last_name: user.name?.split(' ').slice(1).join(' ') || '',
        email: user.email || '',
        phone_number: user.phone || ''
      });
      setError(null);
    }
    setLoading(false);
  }, [user?.id, user?.name, user?.email, user?.phone]);

  const handleProfileUpdate = async () => {
    setSaving(true);
    setError(null);

    try {
      // Validate required fields
      if (!profileData.email || !profileData.first_name) {
        setError('Email and first name are required');
        toast({
          title: "Validation Error",
          description: "Email and first name are required fields.",
          variant: "destructive",
        });
        return;
      }

      // Use the auth/user endpoint to update profile
      const result = await putReq('auth/user/', profileData);

      if (result && (result.status === "200" || result.status === "201")) {
        toast({
          title: "Profile Updated",
          description: "Your profile has been updated successfully.",
        });

        // Update the auth context with new user data
        // The user should refresh or the auth context should be updated
        // For now, we'll show a success message
        
      } else {
        const errorMessage = result?.message || 'Failed to update profile';
        setError(errorMessage);
        toast({
          title: "Update Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || 'An error occurred while updating your profile';
      setError(errorMessage);
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profile Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Management</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              value={profileData.username}
              onChange={(e) => setProfileData({...profileData, username: e.target.value})}
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              value={profileData.email}
              onChange={(e) => setProfileData({...profileData, email: e.target.value})}
            />
          </div>
          <div>
            <Label htmlFor="first_name">First Name</Label>
            <Input
              id="first_name"
              value={profileData.first_name}
              onChange={(e) => setProfileData({...profileData, first_name: e.target.value})}
            />
          </div>
          <div>
            <Label htmlFor="last_name">Last Name</Label>
            <Input
              id="last_name"
              value={profileData.last_name}
              onChange={(e) => setProfileData({...profileData, last_name: e.target.value})}
            />
          </div>
          <div>
            <Label htmlFor="phone_number">Phone Number</Label>
            <Input
              id="phone_number"
              value={profileData.phone_number}
              onChange={(e) => setProfileData({...profileData, phone_number: e.target.value})}
              placeholder="e.g., +1234567890"
            />
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="default"
            className="w-full sm:w-auto"
            onClick={handleProfileUpdate}
            disabled={saving}
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Updating...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Profile
              </>
            )}
          </Button>
          {saving && (
            <span className="text-sm text-muted-foreground">
              Please wait while we update your profile...
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};