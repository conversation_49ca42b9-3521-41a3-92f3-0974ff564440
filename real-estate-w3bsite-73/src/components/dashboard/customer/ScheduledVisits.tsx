import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useAuth } from '@/contexts/AuthContext';
import { getReq, postReq } from '@/apiService';
import { Visit } from '@/types';
import { Calendar, Clock } from 'lucide-react';

export const ScheduledVisits = () => {
  const { user } = useAuth();
  const [visits, setVisits] = useState<Visit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVisits = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const data = await getReq('customer/visits/');
        setVisits(Array.isArray(data) ? data : []);
      } catch (err) {
        
        setError('Failed to load scheduled visits. Please try again.');
        setVisits([]);
      } finally {
        setLoading(false);
      }
    };

    fetchVisits();
  }, [user?.id]);

  const handleCancelVisit = async (visitId: string) => {
    if (confirm('Are you sure you want to cancel this visit?')) {
      try {
        await postReq(`customer/visits/${visitId}/cancel/`, {});
        const updatedVisits = await getReq(`customer/visits/`);
        if (Array.isArray(updatedVisits)) {
          setVisits(updatedVisits);
        }
      } catch (error) {
        
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Scheduled Visits</CardTitle>
      </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Property</TableHead>
                    <TableHead className="hidden sm:table-cell">Agent</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="hidden md:table-cell">Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {visits.map((visit) => (
                    <TableRow key={visit.id}>
                      <TableCell className="font-medium">
                        <div className="max-w-[200px] truncate">{visit.property_title}</div>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">{visit.agent_name || 'Sarah Johnson'}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {visit.date}
                        </div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {visit.time}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={visit.status === 'scheduled' ? 'default' : 'secondary'}>
                          {visit.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {visit.status === 'scheduled' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleCancelVisit(visit.id)}
                          >
                            Cancel
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                  {visits.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <p className="text-muted-foreground">No scheduled visits yet.</p>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
    </Card>
  );
};