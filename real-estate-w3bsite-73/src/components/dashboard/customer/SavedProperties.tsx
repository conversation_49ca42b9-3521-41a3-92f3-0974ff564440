import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PropertyCard } from '@/components/PropertyCard';
import { useAuth } from '@/contexts/AuthContext';
import { getReq, deleteReq } from '@/apiService';
import { Property } from '@/types';
import { Button } from '@/components/ui/button';
import { HeartOff } from 'lucide-react';

// Interface for the backend SavedProperty response
interface SavedPropertyResponse {
  id: string;
  customer: string;
  property: string;
  created_at: string;
  property_details: Property;
}

export const SavedProperties = () => {
  const { user } = useAuth();
  const [savedProperties, setSavedProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSavedProperties = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const data = await getReq('customer/saved-properties/');

        if (Array.isArray(data)) {
          // Transform the backend response to extract property details
          const properties = data.map((savedProperty: SavedPropertyResponse) => {
            const property = savedProperty.property_details;
            // Ensure the property has the correct structure
            return {
              ...property,
              id: property.id || savedProperty.property,
              // Handle image field - use first image from images array
              image: property.images?.[0] || '',
              images: property.images || []
            };
          });
          setSavedProperties(properties);
        } else {
          setSavedProperties([]);
        }
      } catch (err) {
        
        setError('Failed to load saved properties. Please try again.');
        setSavedProperties([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSavedProperties();
  }, [user?.id]);

  const handleRemoveSaved = async (propertyId: string) => {
    try {
      const result = await deleteReq(`customer/saved-properties/${propertyId}/`);
      if (result && (result.status === '200' || result.status === '204')) {
        setSavedProperties(savedProperties.filter(p => p.id !== propertyId));
      } else {
        console.error('SavedProperties: Failed to remove property:', result?.message);
      }
    } catch (error) {
      console.error('SavedProperties: Error removing saved property:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Saved Properties ({savedProperties.length})</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {savedProperties.map((property) => (
              <div key={property.id} className="relative">
                <PropertyCard property={{
                  id: parseInt(property.id),
                  title: property.title,
                  location: property.location,
                  price: `Rs. ${property.price.toLocaleString()}`,
                  image: property.images?.[0] || '',
                  beds: property.bedrooms,
                  baths: property.bathrooms,
                  sqft: property.area?.toString() || '0',
                  formatted_area: property.formatted_area
                }} />
                <Button
                  variant="outline"
                  size="sm"
                  className="absolute top-2 right-2 p-2 bg-white/80 hover:bg-white"
                  onClick={() => handleRemoveSaved(property.id)}
                  title="Remove from saved properties"
                >
                  <HeartOff className="h-4 w-4 text-red-500" />
                </Button>
              </div>
            ))}
            {savedProperties.length === 0 && (
              <div className="col-span-full text-center py-8">
                <p className="text-muted-foreground">No saved properties yet.</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Browse properties and click the heart icon to save them here.
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};