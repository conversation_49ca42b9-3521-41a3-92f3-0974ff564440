import React, { useState, useEffect } from 'react';
import { getReq } from '@/apiService';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Mail } from 'lucide-react';

interface Message {
  id: string;
  sender_name: string;
  message: string;
  time_ago: string;
  is_from_customer: boolean;
  type: 'message' | 'contact';
  subject?: string;
  status?: string;
  created_at?: string;
}

export const CustomerMessages = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

      if (diffInHours < 1) return 'Just now';
      if (diffInHours < 24) return `${diffInHours} hours ago`;
      if (diffInHours < 48) return '1 day ago';
      return `${Math.floor(diffInHours / 24)} days ago`;
    } catch {
      return dateString;
    }
  };

  useEffect(() => {
    const fetchMessages = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch both customer messages and contact inquiries
        const [customerMessages, contactInquiries] = await Promise.all([
          getReq('customer/messages/').catch(() => []),
          getReq('admin/contacts/').catch(() => []) // This will get all contacts, we'll filter by user
        ]);

        const allMessages: Message[] = [];

        // Add customer messages
        if (Array.isArray(customerMessages)) {
          customerMessages.forEach((msg: any) => {
            allMessages.push({
              id: `msg-${msg.id}`,
              sender_name: msg.sender_name || (msg.is_from_customer ? 'You' : 'Agent'),
              message: msg.message,
              time_ago: msg.time_ago || formatDate(msg.created_at),
              is_from_customer: msg.is_from_customer,
              type: 'message',
              created_at: msg.created_at
            });
          });
        }

        // Add contact inquiries (filter by current user's email)
        if (Array.isArray(contactInquiries) && user.email) {
          contactInquiries
            .filter((contact: any) => contact.email === user.email)
            .forEach((contact: any) => {
              allMessages.push({
                id: `contact-${contact.id}`,
                sender_name: 'You',
                message: contact.message,
                time_ago: formatDate(contact.created_at),
                is_from_customer: true,
                type: 'contact',
                subject: contact.subject,
                status: contact.status,
                created_at: contact.created_at
              });
            });
        }

        // Sort by creation date (newest first)
        allMessages.sort((a, b) => {
          const dateA = new Date(a.created_at || '').getTime();
          const dateB = new Date(b.created_at || '').getTime();
          return dateB - dateA;
        });

        setMessages(allMessages);
      } catch (err) {
       
        setError('Failed to load messages. Please try again.');
        setMessages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [user?.id, user?.email]);
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Messages & Inquiries ({messages.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="text-blue-500 hover:underline"
            >
              Try Again
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`border rounded-lg p-4 transition-colors ${
                  msg.is_from_customer ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold">{msg.sender_name}</h4>
                    <Badge variant={msg.type === 'contact' ? 'secondary' : 'default'}>
                      {msg.type === 'contact' ? (
                        <>
                          <Mail className="h-3 w-3 mr-1" />
                          Contact
                        </>
                      ) : (
                        <>
                          <MessageCircle className="h-3 w-3 mr-1" />
                          Message
                        </>
                      )}
                    </Badge>
                    {msg.status && (
                      <Badge variant={msg.status === 'new' ? 'destructive' : 'outline'}>
                        {msg.status}
                      </Badge>
                    )}
                  </div>
                  <span className="text-sm text-muted-foreground">{msg.time_ago}</span>
                </div>

                {msg.subject && (
                  <div className="mb-2">
                    <span className="text-sm font-medium text-gray-600">Subject: </span>
                    <span className="text-sm">{msg.subject}</span>
                  </div>
                )}

                <p className="text-foreground">{msg.message}</p>
              </div>
            ))}
            {messages.length === 0 && (
              <div className="text-center py-8">
                <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-muted-foreground mb-2">No messages yet.</p>
                <p className="text-sm text-muted-foreground">
                  Send us a message through the contact form to see it here.
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};