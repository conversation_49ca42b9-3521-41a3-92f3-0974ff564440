import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { NEPAL_DISTRICTS } from "@/constants/districts"

interface DistrictComboboxProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function DistrictCombobox({
  value = "",
  onValueChange,
  placeholder = "Select district...",
  className,
  disabled = false,
}: DistrictComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState(value)

  // Update input value when value prop changes
  React.useEffect(() => {
    setInputValue(value)
  }, [value])

  const handleSelect = (selectedValue: string) => {
    const newValue = selectedValue === value ? "" : selectedValue
    setInputValue(newValue)
    onValueChange?.(newValue)
    setOpen(false)
  }

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue)
    onValueChange?.(newValue)
  }

  // Filter districts based on input value
  const filteredDistricts = NEPAL_DISTRICTS.filter((district) =>
    district.toLowerCase().includes(inputValue.toLowerCase())
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !inputValue && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          {inputValue || placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search districts..."
            value={inputValue}
            onValueChange={handleInputChange}
          />
          <CommandList>
            <CommandEmpty>
              {inputValue ? (
                <div className="p-2">
                  <p className="text-sm text-muted-foreground mb-2">
                    No district found matching "{inputValue}"
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      handleInputChange(inputValue)
                      setOpen(false)
                    }}
                    className="w-full"
                  >
                    Use "{inputValue}" as custom location
                  </Button>
                </div>
              ) : (
                "No districts found."
              )}
            </CommandEmpty>
            <CommandGroup>
              {filteredDistricts.map((district) => (
                <CommandItem
                  key={district}
                  value={district}
                  onSelect={() => handleSelect(district)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === district ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {district}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
