import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Home, TrendingUp, Settings, BarChart3 } from 'lucide-react';

interface Service {
  title: string;
  description: string;
  icon: string;
}

interface ServiceCardProps {
  service: Service;
  index: number;
}

const getServiceIcon = (iconType: string) => {
  switch (iconType) {
    case '🏠':
      return <Home className="w-8 h-8 text-gold-primary" />;
    case '📈':
      return <TrendingUp className="w-8 h-8 text-gold-primary" />;
    case '🔧':
      return <Settings className="w-8 h-8 text-gold-primary" />;
    case '📊':
      return <BarChart3 className="w-8 h-8 text-gold-primary" />;
    default:
      return <div className="text-4xl">{iconType}</div>;
  }
};

export const ServiceCard = ({ service, index }: ServiceCardProps) => {
  return (
    <Card 
      className="group text-center hover:shadow-xl transition-all duration-500 hover:-translate-y-3 bg-white border-0 shadow-md overflow-hidden relative"
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      {/* Decorative Background Element */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-gold-dark to-gold-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
      
      <CardHeader className="pb-4">
        {/* Icon with Background Circle */}
        <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-br from-gray-light to-white rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
          {getServiceIcon(service.icon)}
        </div>
        
        <CardTitle className="text-xl text-navy-dark group-hover:text-navy-primary transition-colors duration-300">
          {service.title}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="px-6 pb-6">
        <CardDescription className="text-gray-medium text-base leading-relaxed group-hover:text-gray-dark transition-colors duration-300">
          {service.description}
        </CardDescription>
        
        {/* Hover Effect Underline */}
        <div className="mt-4 w-12 h-0.5 bg-gold-primary mx-auto scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
      </CardContent>
    </Card>
  );
};