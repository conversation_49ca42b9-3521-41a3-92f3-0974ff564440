import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthPrompt } from '@/hooks/useAuthPrompt';
import { AuthPrompt } from '@/components/auth/AuthPrompt';
import { postReq, deleteReq, getReq } from '@/apiService';
import { useToast } from '@/hooks/use-toast';

interface SavePropertyButtonProps {
  propertyId: string | number;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  showText?: boolean;
}

export const SavePropertyButton: React.FC<SavePropertyButtonProps> = ({
  propertyId,
  className = '',
  size = 'default',
  variant = 'ghost',
  showText = false
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const {
    isPromptOpen,
    promptConfig,
    requireAuth,
    closePrompt,
    handleLogin,
    handleRegister
  } = useAuthPrompt();

  // Only check if saved when user clicks the button (lazy loading)
  // This prevents API calls on every PropertyCard mount

  const checkIfSaved = async () => {
    try {
      const savedProperties = await getReq('customer/saved-properties/');
      if (Array.isArray(savedProperties)) {
        const isPropertySaved = savedProperties.some(
          (saved: any) => {
            // Handle both direct property ID and nested property_details structure
            const savedPropertyId = saved.property_details?.id || saved.property?.id || saved.property;
            return savedPropertyId === propertyId || savedPropertyId === propertyId.toString();
          }
        );
        setIsSaved(isPropertySaved);
      }
    } catch (error) {
      // Silently fail - not critical for initial render
      console.debug('SavePropertyButton: Error checking saved status:', error);
    }
  };

  const handleSaveProperty = async () => {
    // Prevent multiple simultaneous requests
    if (isLoading) return;

    // Check if saved only when user clicks (lazy loading)
    if (user && !isLoading) {
      await checkIfSaved();
    }

    const success = requireAuth(
      {
        feature: 'save',
        title: 'Save Your Favorite Properties',
        description: 'Create an account to save properties and get notified about similar listings.',
        onSuccess: () => performSaveAction()
      },
      () => performSaveAction()
    );

    // Don't call performSaveAction again if requireAuth already called it
    if (success && user) {
      // requireAuth already called performSaveAction for authenticated users
      return;
    }
  };

  const performSaveAction = async () => {
    if (!user || !propertyId) return;

    setIsLoading(true);
    try {
      if (isSaved) {
        // Remove from saved properties
        const result = await deleteReq(`customer/saved-properties/${propertyId}/`);
        if (result && (result.status === '200' || result.status === '204')) {
          setIsSaved(false);
          toast({
            title: "Property Removed",
            description: "Property has been removed from your saved list.",
          });
        } else {
          throw new Error(result?.message || 'Failed to remove property');
        }
      } else {
        // Add to saved properties - use 'property' field, not 'property_id'
        const result = await postReq('customer/saved-properties/create/', {
          property: propertyId
        });
        if (result && (result.status === '200' || result.status === '201')) {
          setIsSaved(true);
          toast({
            title: "Property Saved",
            description: "Property has been added to your saved list.",
          });
        } else {
          throw new Error(result?.message || 'Failed to save property');
        }
      }
    } catch (error: any) {
      console.error('SavePropertyButton: Error updating saved properties:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update saved properties. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        onClick={handleSaveProperty}
        disabled={isLoading}
        variant={variant}
        size={size}
        className={`${className} ${isSaved ? 'text-red-500 hover:text-red-600' : 'text-gray-500 hover:text-red-500'}`}
      >
        <Heart 
          className={`h-4 w-4 ${showText ? 'mr-2' : ''} ${isSaved ? 'fill-current' : ''}`} 
        />
        {showText && (
          <span>
            {isLoading ? 'Saving...' : isSaved ? 'Saved' : 'Save'}
          </span>
        )}
      </Button>

      <AuthPrompt
        isOpen={isPromptOpen}
        onClose={closePrompt}
        onLogin={handleLogin}
        onRegister={handleRegister}
        feature={promptConfig.feature}
        title={promptConfig.title}
        description={promptConfig.description}
      />


    </>
  );
};
