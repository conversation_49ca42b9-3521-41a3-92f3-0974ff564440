import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { MessageCircle, Send } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthPrompt } from '@/hooks/useAuthPrompt';
import { AuthPrompt } from '@/components/auth/AuthPrompt';
import { postReq } from '@/apiService';
import { useToast } from '@/hooks/use-toast';

interface ContactAgentButtonProps {
  propertyId?: string | number;
  propertyTitle?: string;
  agentId?: string | number;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  children?: React.ReactNode;
}

export const ContactAgentButton: React.FC<ContactAgentButtonProps> = ({
  propertyId,
  propertyTitle,
  agentId,
  className = '',
  size = 'default',
  variant = 'default',
  children
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isContactFormOpen, setIsContactFormOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    message: '',
    phone: '',
    preferred_contact_time: ''
  });

  const {
    isPromptOpen,
    promptConfig,
    requireAuth,
    closePrompt,
    handleLogin,
    handleRegister
  } = useAuthPrompt();

  const handleContactClick = () => {
    const success = requireAuth(
      {
        feature: 'contact',
        title: 'Contact Our Expert Agents',
        description: 'Sign in to contact our agents and get personalized assistance with your property search.',
        onSuccess: () => setIsContactFormOpen(true)
      },
      () => setIsContactFormOpen(true)
    );

    if (success) {
      setIsContactFormOpen(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !propertyId) return;

    setIsSubmitting(true);
    try {
      const result = await postReq('customer/inquiries/create/', {
        property: propertyId, // Use 'property' not 'property_id'
        message: formData.message || `I'm interested in the property: ${propertyTitle}`,
        agent: agentId // Use 'agent' not 'agent_id'
      });

      if (result && (result.status === '200' || result.status === '201')) {
        toast({
          title: "Message Sent",
          description: "Your inquiry has been sent to our agent. We'll get back to you soon!",
        });

        setIsContactFormOpen(false);
        setFormData({ message: '', phone: '', preferred_contact_time: '' });
      } else {
        throw new Error(result?.message || 'Failed to send inquiry');
      }
    } catch (error: any) {
      console.error('Error sending inquiry:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to send your message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Button
        onClick={handleContactClick}
        variant={variant}
        size={size}
        className={className}
      >
        {children || (
          <>
            <MessageCircle className="h-4 w-4 mr-2" />
            Contact Agent
          </>
        )}
      </Button>

      {/* Contact Form Dialog */}
      <Dialog open={isContactFormOpen} onOpenChange={setIsContactFormOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5 text-navy-primary" />
              Contact Agent
            </DialogTitle>
            <DialogDescription>
              {propertyTitle ? `Send an inquiry about: ${propertyTitle}` : 'Send your inquiry to our agent'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                placeholder={`Hi, I'm interested in ${propertyTitle || 'this property'}. Could you please provide more information?`}
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="phone">Phone Number (Optional)</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="Your phone number"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="contact_time">Preferred Contact Time (Optional)</Label>
              <Input
                id="contact_time"
                placeholder="e.g., Weekdays 9-5 PM"
                value={formData.preferred_contact_time}
                onChange={(e) => setFormData({ ...formData, preferred_contact_time: e.target.value })}
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsContactFormOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  'Sending...'
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Message
                  </>
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Auth Prompt */}
      <AuthPrompt
        isOpen={isPromptOpen}
        onClose={closePrompt}
        onLogin={handleLogin}
        onRegister={handleRegister}
        feature={promptConfig.feature}
        title={promptConfig.title}
        description={promptConfig.description}
      />


    </>
  );
};
