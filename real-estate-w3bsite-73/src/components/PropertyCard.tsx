import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Bed, Bath, Square, MapPin } from 'lucide-react';
import { getBestAreaDisplay } from '@/utils/propertyUtils';

interface Property {
  id: number;
  title: string;
  location: string;
  price: string;
  image: string;
  beds?: number; // Now optional
  baths: number;
  sqft: string;
  area?: number;
  area_unit?: string;
  formatted_area?: string;
  // Property purpose
  property_purpose?: 'land' | 'rent';
  purpose_display?: string;
  // New land area fields
  land_ropani?: number;
  land_aana?: number;
  land_paisa?: number;
  land_daam?: number;
  formatted_land_area?: string;
  land_area_display?: string;
}

interface PropertyCardProps {
  property: Property;
  index?: number;
  className?: string;
}

export const PropertyCard = ({ property, index = 0, className = '' }: PropertyCardProps) => {
  return (
    <div 
      className={`group relative overflow-hidden rounded-lg bg-white shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-105 ${className}`}
      style={{ animationDelay: `${index * 0.1}s` }}
    >
      {/* Property Image */}
      <div className="relative h-80 overflow-hidden">
        <img 
          src={property.image} 
          alt={property.title}
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />
        
        {/* Price Badge */}
        <div className="absolute top-4 right-4 bg-navy-primary text-white px-3 py-1 rounded-full text-sm font-semibold shadow-lg">
          {property.price}
        </div>

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-navy-dark/90 via-navy-primary/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
            <h3 className="text-xl font-bold mb-2">{property.title}</h3>
            <p className="text-gray-light mb-4">{property.location}</p>
            
            {/* Property Details */}
            <div className="flex items-center gap-4 mb-4 text-sm">
              <div className="flex items-center gap-1">
                <MapPin size={16} />
                <span>{property.purpose_display || property.property_purpose || 'Land'}</span>
              </div>
              {property.property_purpose === 'rent' && property.beds && (
                <div className="flex items-center gap-1">
                  <Bed size={16} />
                  <span>{property.beds} beds</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Bath size={16} />
                <span>{property.baths} baths</span>
              </div>
              <div className="flex items-center gap-1">
                <Square size={16} />
                <span>{getBestAreaDisplay(property as any) || `${property.sqft} sqft`}</span>
              </div>
            </div>

            {/* Learn More Button */}
            <Link to={`/property/${property.id}`}>
              <Button 
                variant="premium" 
                size="sm" 
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 delay-100"
              >
                Learn More
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};