// API Service for handling HTTP requests
// Supports both development and production environments

// Get API base URL from environment variables or use relative path
const getApiBaseUrl = () => {
    // In production, use environment variable or fallback to production URL
    if (import.meta.env.PROD) {
        const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.68:8000/api';
        // Ensure URL doesn't end with slash to avoid double slashes
        return apiUrl.replace(/\/$/, '');
    }
    // In development, use relative path (handled by Vite proxy)
    return '/api';
};

// Timeout configuration
const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');

// Logging configuration
const ENABLE_LOGGING = import.meta.env.VITE_ENABLE_LOGGING === 'true' || !import.meta.env.PROD;

// Generic HTTP request function
const makeRequest = async (url: string, method: string, data?: unknown) => {
    const baseUrl = getApiBaseUrl();
    const fullUrl = `${baseUrl}/${url}`.replace(/\/+/g, '/').replace(':/', '://');

    if (ENABLE_LOGGING) {
        console.log(`API Request: ${method} ${fullUrl}`);
    }

    // Get auth token if available, but only for protected endpoints
    const token = localStorage.getItem('authToken');
    const headers: Record<string, string> = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
    };

    // Only add auth header for protected endpoints (not for login/register)
    const isAuthEndpoint = url.includes('auth/login') || url.includes('auth/register');
    if (token && !isAuthEndpoint) {
        headers['Authorization'] = `Token ${token}`;
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

        const requestOptions: RequestInit = {
            method,
            headers,
            signal: controller.signal,
        };

        // Handle method override for Django
        if (data && typeof data === 'object' && data !== null && '_method' in data) {
            const { _method, ...restData } = data as any;
            requestOptions.method = _method;
            if (Object.keys(restData).length > 0) {
                requestOptions.body = JSON.stringify(restData);
            }
        } else if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            requestOptions.body = JSON.stringify(data);
        }

        const response = await fetch(fullUrl, requestOptions);
        clearTimeout(timeoutId);

        if (method === 'DELETE' && response.ok) {
            return { status: String(response.status), message: 'Deleted successfully' };
        }

        const responseData = await response.json();

        if (response.ok) {
            if (ENABLE_LOGGING) {
                console.log(`API Success: ${method} ${fullUrl}`, responseData);
            }
            return { data: responseData, status: String(response.status) };
        }

        const errorMsg = responseData.message || `HTTP error ${response.status}`;
        if (ENABLE_LOGGING) {
            console.error(`API Error: ${method} ${fullUrl}`, errorMsg);
        }
        return { status: String(response.status || "400"), message: errorMsg };
    } catch (error: any) {
        if (error.name === 'AbortError') {
            const timeoutMsg = 'Request timeout - please check your connection';
            if (ENABLE_LOGGING) {
                console.error(`API Timeout: ${method} ${fullUrl}`);
            }
            return { status: "408", message: timeoutMsg };
        }

        const errorMsg = error.message || 'Network error occurred';
        if (ENABLE_LOGGING) {
            console.error(`API Network Error: ${method} ${fullUrl}`, error);
        }
        return { status: "400", message: errorMsg };
    }
};

// JSON POST request
export const postReq = async (url: string, data: unknown) => {
    return makeRequest(url, 'POST', data);
};

// PUT request
export const putReq = async (url: string, data: unknown) => {
    return makeRequest(url, 'PUT', data);
};

// PATCH request
export const patchReq = async (url: string, data: unknown) => {
    return makeRequest(url, 'PATCH', data);
};

// DELETE request
export const deleteReq = async (url: string) => {
    return makeRequest(url, 'DELETE');
};

// GET request
export const getReq = async (url: string) => {
    const baseUrl = getApiBaseUrl();
    const fullUrl = `${baseUrl}/${url}`.replace(/\/+/g, '/').replace(':/', '://');

    if (ENABLE_LOGGING) {
        console.log(`API GET Request: ${fullUrl}`);
    }

    // Get auth token if available
    const token = localStorage.getItem('authToken');
    const headers: Record<string, string> = {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
    };

    // Define which endpoints require authentication
    const protectedEndpoints = [
        'customer/',
        'admin/',
        'auth/user/',
        'auth/logout/'
    ];

    // Only add auth header for protected endpoints
    const requiresAuth = protectedEndpoints.some(endpoint => url.startsWith(endpoint));
    if (token && requiresAuth) {
        headers['Authorization'] = `Token ${token}`;
    }

    try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

        const response = await fetch(fullUrl, {
            headers,
            signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            if (ENABLE_LOGGING) {
                console.error(`API GET Error: ${fullUrl} - Status: ${response.status}`);
            }
            return null;
        }

        const data = await response.json();

        if (ENABLE_LOGGING) {
            console.log(`API GET Success: ${fullUrl}`, data);
        }

        // If the response is a paginated object with a `results` array, unwrap it for convenience
        const unwrapped = (data && typeof data === 'object' && Array.isArray(data.results)) ? data.results : data;
        return unwrapped;
    } catch (error: any) {
        if (error.name === 'AbortError') {
            if (ENABLE_LOGGING) {
                console.error(`API GET Timeout: ${fullUrl}`);
            }
        } else if (ENABLE_LOGGING) {
            console.error(`API GET Network Error: ${fullUrl}`, error);
        }
        return null;
    }
};

// Generic multipart request function
const makeMultipartRequest = async (url: string, method: string, formData: FormData) => {
    const baseUrl = getApiBaseUrl();
    const fullUrl = `${baseUrl}/${url}`.replace(/\/+/g, '/').replace(':/', '://'); // Use consistent URL building

    // Get auth token if available
    const token = localStorage.getItem('authToken');
    const headers: Record<string, string> = {};

    // Define which endpoints require authentication
    const protectedEndpoints = [
        'customer/',
        'admin/',
        'auth/user/',
        'auth/logout/'
    ];

    // Only add auth header for protected endpoints
    const requiresAuth = protectedEndpoints.some(endpoint => url.startsWith(endpoint));
    if (token && requiresAuth) {
        headers['Authorization'] = `Token ${token}`;
    }

    try {
        const response = await fetch(fullUrl, {
            method,
            headers,
            body: formData
        });

        const responseData = await response.json();

        if (response.ok) {
            return { data: responseData, status: String(response.status) };
        }
        const errorMsg = responseData.message || `HTTP error ${response.status}`;
        return { status: String(response.status || "400"), message: errorMsg };

    } catch (error: any) {
        return { status: "400", message: error.message || 'An error occurred' };
    }
};

// Multipart/form-data POST request
export const postReqMultipart = async (url: string, formData: FormData) => {
    return makeMultipartRequest(url, 'POST', formData);
};

// Multipart/form-data PUT request
export const putReqMultipart = async (url: string, formData: FormData) => {
    return makeMultipartRequest(url, 'PUT', formData);
};