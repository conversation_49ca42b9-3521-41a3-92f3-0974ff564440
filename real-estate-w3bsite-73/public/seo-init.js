// Initial SEO setup script
// This script runs before React loads to set better initial meta tags

(function() {
  // Static organization data for immediate SEO
  const staticOrgData = {
    name: 'सिमथली रियल स्टेट',
    description: 'सिमथली रियल स्टेट सबैका लागि सम्पत्तिको सुरक्षा र सुनिश्चितता प्रदान गर्न प्रतिबद्ध छ। हामी विश्वास गर्छौं कि प्रत्येक व्यक्तिलाई आफ्नो सम्पत्तिको अधिकार हुनुपर्छ।',
    logo: '/image.png',
    twitter: '@simthalirealestate'
  };

  // Function to update meta tags with given data
  function updateMetaTags(orgData) {
    const title = `${orgData.name} - Real Estate`;
    document.title = title;

    const description = orgData.description;
    const currentUrl = window.location.href;
    const twitterHandle = orgData.twitter || '@realestate';

    // Construct logo URL
    let logoUrl = orgData.logo || '/image.png';
    if (logoUrl && !logoUrl.startsWith('http')) {
      if (logoUrl.startsWith('/media/')) {
        logoUrl = `${window.location.origin}${logoUrl}`;
      } else if (logoUrl.startsWith('media/')) {
        logoUrl = `${window.location.origin}/${logoUrl}`;
      } else if (!logoUrl.startsWith('/')) {
        logoUrl = `${window.location.origin}/${logoUrl}`;
      } else {
        logoUrl = `${window.location.origin}${logoUrl}`;
      }
    }

    // Update meta tags
    const metaUpdates = [
      { id: 'page-title', content: title },
      { id: 'page-description', content: description },
      { id: 'page-author', content: orgData.name },
      { id: 'og-url', content: currentUrl },
      { id: 'og-title', content: title },
      { id: 'og-description', content: description },
      { id: 'og-site-name', content: orgData.name },
      { id: 'og-image', content: logoUrl },
      { id: 'twitter-url', content: currentUrl },
      { id: 'twitter-title', content: title },
      { id: 'twitter-description', content: description },
      { id: 'twitter-image', content: logoUrl },
      { id: 'twitter-site', content: twitterHandle },
      { id: 'twitter-creator', content: twitterHandle },
      { id: 'canonical-url', href: currentUrl }
    ];

    metaUpdates.forEach(({ id, content, href }) => {
      const element = document.getElementById(id);
      if (element) {
        if (element.tagName === 'TITLE') {
          element.textContent = content || '';
        } else if (element.tagName === 'LINK' && href) {
          element.setAttribute('href', href);
        } else if (content) {
          element.setAttribute('content', content);
        }
      }
    });

    // Update favicon
    const favicon = document.querySelector('link[rel="icon"]');
    if (favicon && logoUrl) {
      favicon.href = logoUrl;
    }

    console.log('SEO meta tags updated:', {
      title,
      description,
      image: logoUrl,
      organization: orgData.name
    });
  }

  // Set static data immediately for instant SEO
  updateMetaTags(staticOrgData);
  // Optionally try to fetch API data to update if available
  const API_BASE = 'https://simthalirealestate.com/backend/api';

  fetch(`${API_BASE}/organization/`)
    .then(response => {
      if (!response.ok) {
        throw new Error('Failed to fetch organization data');
      }
      return response.json();
    })
    .then(orgData => {
      if (orgData && orgData.name) {
        console.log('Updating with API organization data:', orgData);
        // Update with API data if available, but static data is already set
        updateMetaTags(orgData);
      }
    })
    .catch(error => {
      console.log('Using static organization data (API not available):', error.message);
      // Static data is already set, so no need to do anything
    });
})();
