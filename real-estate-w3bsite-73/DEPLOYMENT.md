# 🚀 Production Deployment Guide

This guide will help you deploy your React Real Estate application to production servers, including cPanel hosting.

## 📋 Pre-Deployment Checklist

1. **Update Environment Variables**
   - Edit `.env.production` file
   - Set `VITE_API_BASE_URL` to your production API URL
   - Example: `VITE_API_BASE_URL=https://yourdomain.com/api`

2. **Test Build Locally**
   ```bash
   npm run build
   npm run preview
   ```

## 🏗️ Building for Production

### Option 1: Using the Deploy Script
```bash
chmod +x deploy.sh
./deploy.sh
```

### Option 2: Manual Build
```bash
npm install
npm run build
```

## 🌐 Deployment Options

### 1. cPanel / Shared Hosting (Apache)

1. **Build the project** (creates `dist` folder)
2. **Upload files**:
   - Upload ALL files from `dist` folder to your domain's public_html directory
   - Make sure `.htaccess` file is uploaded (handles routing)
3. **Verify**:
   - Check that `.htaccess` file exists in your web root
   - Test navigation by visiting different routes directly

### 2. Netlify

1. **Build and deploy**:
   ```bash
   npm run build
   ```
2. **Upload `dist` folder** to Netlify
3. **The `_redirects` file** will handle routing automatically

### 3. Vercel

1. **Connect your repository** to Vercel
2. **Set build command**: `npm run build`
3. **Set output directory**: `dist`
4. **Add environment variables** in Vercel dashboard

### 4. Traditional VPS/Dedicated Server

1. **Build the project**
2. **Configure web server** (Apache/Nginx) to:
   - Serve files from `dist` directory
   - Redirect all routes to `index.html`
   - Handle API proxy if needed

## 🔧 Server Configuration

### Apache (.htaccess) - Already included
The `.htaccess` file in `public` folder handles:
- Client-side routing redirects
- Security headers
- Asset caching
- Gzip compression

### Nginx Configuration
If using Nginx, add this to your server block:
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

## 🔗 API Configuration

### Development vs Production
- **Development**: Uses Vite proxy (`/api` → `http://localhost:8000`)
- **Production**: Uses environment variable or relative paths

### Setting Production API URL
1. **Edit `.env.production`**:
   ```
   VITE_API_BASE_URL=https://yourdomain.com/api
   ```

2. **Or use relative paths** (if frontend and backend are on same domain):
   ```
   VITE_API_BASE_URL=/api
   ```

## ✅ Post-Deployment Verification

1. **Test all routes**:
   - Visit `/` (home page)
   - Visit `/about` (should not 404)
   - Visit `/properties` (should not 404)
   - Refresh any page (should not 404)

2. **Test API connectivity**:
   - Check browser network tab
   - Verify API calls are reaching your backend

3. **Test authentication**:
   - Login/logout functionality
   - Protected routes

## 🐛 Troubleshooting

### 404 Errors on Route Refresh
- **Cause**: Server not configured for client-side routing
- **Solution**: Ensure `.htaccess` (Apache) or equivalent config exists

### API Calls Failing
- **Cause**: Incorrect API base URL
- **Solution**: Check `.env.production` and update `VITE_API_BASE_URL`

### Assets Not Loading
- **Cause**: Incorrect base path
- **Solution**: Verify all files from `dist` folder are uploaded

### CORS Errors
- **Cause**: Backend not configured for production domain
- **Solution**: Update Django CORS settings for production domain

## 📱 Performance Optimization

The build includes:
- ✅ Code splitting
- ✅ Asset optimization
- ✅ Gzip compression
- ✅ Browser caching
- ✅ Security headers

## 🔒 Security Considerations

- HTTPS is recommended for production
- Update CORS settings in Django backend
- Set proper security headers (included in .htaccess)
- Remove development tools in production

## 📞 Support

If you encounter issues:
1. Check browser console for errors
2. Verify server configuration
3. Test API endpoints directly
4. Check network tab for failed requests

---

**Ready for Production!** 🎉

Your React application is now configured to handle client-side routing properly in production environments.
