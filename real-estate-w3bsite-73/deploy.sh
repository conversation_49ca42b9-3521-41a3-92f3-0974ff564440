#!/bin/bash

# Production deployment script for React app
echo "🚀 Starting production build..."

# Clean previous build
echo "🧹 Cleaning previous build..."
rm -rf dist

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Copy .htaccess to public folder to ensure it's included in build
echo "📋 Preparing deployment files..."
cp dist/.htaccess public/.htaccess 2>/dev/null || echo "Creating .htaccess in public folder..."

# Build the project for production with proper environment
echo "🔨 Building project for production..."
NODE_ENV=production npm run build:prod

# Ensure .htaccess is in the dist folder
echo "📄 Ensuring .htaccess is properly configured..."
if [ ! -f "dist/.htaccess" ]; then
    cp public/.htaccess dist/.htaccess
fi

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Build files are in the 'dist' directory"
    echo ""
    echo "📋 Files to upload to cPanel:"
    echo "- All files from 'dist' directory"
    echo "- Make sure .htaccess is uploaded (CRITICAL for routing)"
    echo ""
    echo "🔧 cPanel Deployment Steps:"
    echo "1. Go to File Manager in cPanel"
    echo "2. Navigate to public_html directory"
    echo "3. Delete existing files (backup first if needed)"
    echo "4. Upload ALL files from the 'dist' directory"
    echo "5. Ensure .htaccess file is uploaded and visible"
    echo "6. Set file permissions: 644 for files, 755 for folders"
    echo ""
    echo "🌐 API Configuration:"
    echo "- Frontend will connect to: https://simthalirealestate.com/backend/api"
    echo "- Ensure Django backend is running and accessible"
    echo "- Check CORS settings in Django backend"
    echo ""
    echo "✨ Your app is ready for production deployment!"
else
    echo "❌ Build failed! Please check the errors above."
    exit 1
fi
