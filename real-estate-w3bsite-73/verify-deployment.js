#!/usr/bin/env node

/**
 * Deployment Verification Script
 * Checks if all required files are present in the dist folder
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const distPath = path.join(__dirname, 'dist');
const requiredFiles = [
    'index.html',
    '.htaccess',
    'assets',
    'robots.txt'
];

const optionalFiles = [
    '_redirects',
    'web.config',
    'image.png',
    'placeholder.svg'
];

console.log('🔍 Verifying deployment files...\n');

// Check if dist folder exists
if (!fs.existsSync(distPath)) {
    console.error('❌ dist folder not found! Run "npm run build" first.');
    process.exit(1);
}

let allGood = true;

// Check required files
console.log('📋 Required Files:');
requiredFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    const exists = fs.existsSync(filePath);
    console.log(`${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allGood = false;
});

console.log('\n📋 Optional Files:');
optionalFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    const exists = fs.existsSync(filePath);
    console.log(`${exists ? '✅' : '⚠️ '} ${file}`);
});

// Check assets folder contents
const assetsPath = path.join(distPath, 'assets');
if (fs.existsSync(assetsPath)) {
    const assetFiles = fs.readdirSync(assetsPath);
    console.log(`\n📁 Assets folder contains ${assetFiles.length} files:`);
    assetFiles.forEach(file => {
        console.log(`   📄 ${file}`);
    });
}

// Check .htaccess content
const htaccessPath = path.join(distPath, '.htaccess');
if (fs.existsSync(htaccessPath)) {
    const content = fs.readFileSync(htaccessPath, 'utf8');
    const hasRewrite = content.includes('RewriteEngine On');
    const hasRouting = content.includes('RewriteRule . /index.html');
    
    console.log('\n🔧 .htaccess Configuration:');
    console.log(`${hasRewrite ? '✅' : '❌'} RewriteEngine enabled`);
    console.log(`${hasRouting ? '✅' : '❌'} Client-side routing configured`);
    
    if (!hasRewrite || !hasRouting) allGood = false;
}

console.log('\n' + '='.repeat(50));

if (allGood) {
    console.log('🎉 All checks passed! Ready for deployment.');
    console.log('\n📋 Next steps:');
    console.log('1. Upload all files from "dist" folder to your web server');
    console.log('2. Ensure .htaccess file is uploaded');
    console.log('3. Test all routes after deployment');
    console.log('4. Verify API connectivity');
} else {
    console.log('❌ Some issues found. Please fix them before deployment.');
    process.exit(1);
}

console.log('\n📖 See PRODUCTION_CHECKLIST.md for detailed deployment guide.');
