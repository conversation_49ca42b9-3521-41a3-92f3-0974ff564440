# Premier Properties - Real Estate Platform

## Project Overview

Premier Properties is a comprehensive real estate web application built with modern React technologies. The platform provides property listings, agent management, customer dashboards, and administrative controls for a complete real estate management solution.

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: shadcn/ui component library
- **Routing**: React Router DOM
- **State Management**: React Context API
- **Data Fetching**: TanStack Query
- **Icons**: Lucide React

## Project Structure

### Core Files
```
├── src/
│   ├── App.tsx                 # Main app component with routing
│   ├── main.tsx               # Application entry point
│   ├── index.css              # Global styles and design tokens
│   └── vite-env.d.ts          # TypeScript environment declarations
```

### Pages (`src/pages/`)
```
├── pages/
│   ├── Home.tsx               # Landing page with hero carousel
│   ├── Properties.tsx         # Property listings page
│   ├── PropertyDetails.tsx    # Individual property details
│   ├── Agents.tsx            # Agent directory
│   ├── About.tsx             # About us page
│   ├── Services.tsx          # Services offered
│   ├── Contact.tsx           # Contact information
│   ├── Index.tsx             # Index route handler
│   └── NotFound.tsx          # 404 error page
```

### Components Structure

#### Layout Components (`src/components/layout/`)
```
├── layout/
│   ├── Layout.tsx            # Main layout wrapper with navigation/footer
│   ├── Navigation.tsx        # Header navigation with auth dialogs
│   └── Footer.tsx            # Footer with company info and links
```

#### Dashboard Components

##### Admin Dashboard (`src/components/dashboard/admin/`)
```
├── dashboard/admin/
│   ├── AdminOverview.tsx         # Dashboard overview with analytics
│   ├── ServicesManagement.tsx    # CRUD for services
│   ├── JourneyManagement.tsx     # CRUD for customer journey steps
│   ├── HeroManagement.tsx        # CRUD for hero carousel images
│   ├── PropertiesManagement.tsx  # CRUD for property listings
│   ├── PropertyTypesManagement.tsx # CRUD for property categories
│   ├── AgentsManagement.tsx      # CRUD for agent profiles
│   ├── UserManagement.tsx        # User account management
│   ├── OrganizationManagement.tsx # Company settings
│   └── AboutUsManagement.tsx     # About page content management
```

##### Customer Dashboard (`src/components/dashboard/customer/`)
```
├── dashboard/customer/
│   ├── ProfileManagement.tsx     # Customer profile editing
│   ├── SavedProperties.tsx       # Favorited properties
│   ├── CustomerInquiries.tsx     # Property inquiries tracking
│   ├── ScheduledVisits.tsx       # Appointment scheduling
│   ├── PropertyAlerts.tsx        # Search alerts and notifications
│   ├── CustomerDocuments.tsx     # Document downloads
│   └── CustomerMessages.tsx      # Communication with agents
```

##### Main Dashboard Files
```
├── dashboard/
│   ├── AdminDashboard.tsx        # Admin dashboard main component
│   └── CustomerDashboard.tsx     # Customer dashboard main component
```

#### UI Components (`src/components/ui/`)
Complete shadcn/ui component library including:
- Forms (Input, Label, Textarea, Select, etc.)
- Navigation (Button, Dropdown, Menu, etc.)
- Data Display (Table, Card, Badge, etc.)
- Feedback (Toast, Alert, Dialog, etc.)
- Layout (Separator, Aspect Ratio, etc.)

#### Feature Components (`src/components/`)
```
├── HeroCarousel.tsx          # Homepage image carousel
├── PropertyCard.tsx          # Property listing card component
├── ServiceCard.tsx           # Service feature card
└── ProtectedRoute.tsx        # Route authentication wrapper
```

#### Authentication (`src/components/auth/`)
```
├── auth/
│   ├── LoginDialog.tsx       # User login modal
│   └── RegisterDialog.tsx    # Customer registration modal
```

### Context & State Management (`src/contexts/`)
```
├── contexts/
│   └── AuthContext.tsx       # Authentication state management
```

### Data & Utilities

#### Mock Data (`src/data/`)
```
├── data/
│   └── mockData.ts           # Sample data for development
```

#### Utilities (`src/lib/` & `src/hooks/`)
```
├── lib/
│   └── utils.ts              # Utility functions and helpers
├── hooks/
│   ├── use-mobile.tsx        # Mobile responsive hook
│   ├── use-toast.ts          # Toast notification hook
│   └── useScrollAnimation.tsx # Scroll animation effects
```

### Configuration Files
```
├── tailwind.config.ts        # Tailwind CSS configuration
├── vite.config.ts           # Vite build configuration
├── eslint.config.js         # ESLint linting rules
└── tsconfig files           # TypeScript configuration
```

## Design System

### Color Palette (HSL Values)
```css
/* Primary Colors */
--navy-primary: 220 85% 25%     /* Main brand navy */
--navy-light: 220 85% 35%       /* Lighter navy variant */
--navy-dark: 220 85% 15%        /* Darker navy variant */

/* Accent Colors */
--gold-primary: 45 95% 55%      /* Luxury gold accent */
--gold-light: 45 95% 65%        /* Light gold variant */
--gold-dark: 45 95% 45%         /* Dark gold variant */

/* Neutral Colors */
--gray-light: 0 0% 98%          /* Light background */
--gray-medium: 0 0% 60%         /* Medium gray text */
--gray-dark: 0 0% 20%           /* Dark gray text */
```

### Component Variants
- **Buttons**: primary, secondary, accent, outline, ghost, destructive
- **Cards**: Consistent shadows and spacing
- **Forms**: Unified input styling with focus states
- **Tables**: Responsive with proper mobile breakpoints

## Features

### Authentication System
- **Customer Registration**: Email/password signup
- **Admin Login**: Administrative access
- **Protected Routes**: Role-based access control
- **Context Management**: Global auth state

### Property Management
- **Listings**: Comprehensive property database
- **Search & Filter**: Advanced property search
- **Image Galleries**: Multiple property photos
- **Details Pages**: Full property specifications

### Dashboard Functionality

#### Admin Features
- **Analytics Overview**: Property and user statistics
- **Content Management**: Full CRUD operations for:
  - Properties and property types
  - Agent profiles and assignments
  - Service offerings
  - Hero carousel images
  - Customer journey steps
  - User accounts
  - Company information

#### Customer Features
- **Profile Management**: Personal information updates
- **Property Favorites**: Save/unsave properties
- **Inquiry Tracking**: Property interest management
- **Visit Scheduling**: Appointment booking system
- **Document Access**: Brochure and guide downloads
- **Messaging**: Communication with agents
- **Alerts**: Custom property notifications

### Responsive Design
- **Mobile-First**: Optimized for all screen sizes
- **Breakpoint System**: Consistent responsive behavior
- **Touch-Friendly**: Mobile interaction optimized
- **Performance**: Optimized loading and rendering

## Development Workflow

### Getting Started
```bash
# Clone repository
git clone <YOUR_GIT_URL>
cd <YOUR_PROJECT_NAME>

# Install dependencies
npm install

# Start development server
npm run dev
```

### Build & Deploy
```bash
# Production build
npm run build

# Preview production build
npm run preview
```

## Suggested Django Backend Models

Based on the frontend structure, here are the recommended Django models:

### Core Models
```python
# User Management
class User(AbstractUser):
    role = models.CharField(choices=[('admin', 'Admin'), ('customer', 'Customer'), ('agent', 'Agent')])
    phone = models.CharField(max_length=20, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    avatar = models.ImageField(upload_to='avatars/', blank=True)
    bio = models.TextField(blank=True)
    preferences = models.JSONField(default=dict)

# Property Management
class PropertyType(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

class Property(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    property_type = models.ForeignKey(PropertyType, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=12, decimal_places=2)
    bedrooms = models.IntegerField()
    bathrooms = models.IntegerField()
    area = models.DecimalField(max_digits=10, decimal_places=2)
    location = models.CharField(max_length=200)
    address = models.TextField()
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    is_featured = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class PropertyImage(models.Model):
    property = models.ForeignKey(Property, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='properties/')
    is_primary = models.BooleanField(default=False)
    order = models.IntegerField(default=0)

# Agent Management
class Agent(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    specializations = models.ManyToManyField(PropertyType, blank=True)
    experience_years = models.IntegerField(default=0)
    license_number = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)

# Customer Interactions
class PropertyInquiry(models.Model):
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    message = models.TextField()
    status = models.CharField(choices=[('pending', 'Pending'), ('responded', 'Responded'), ('closed', 'Closed')])
    created_at = models.DateTimeField(auto_now_add=True)

class PropertyVisit(models.Model):
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    scheduled_date = models.DateField()
    scheduled_time = models.TimeField()
    status = models.CharField(choices=[('scheduled', 'Scheduled'), ('completed', 'Completed'), ('cancelled', 'Cancelled')])
    notes = models.TextField(blank=True)

class SavedProperty(models.Model):
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['customer', 'property']

# Content Management
class Service(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    icon = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)

class HeroSlide(models.Model):
    title = models.CharField(max_length=200)
    subtitle = models.CharField(max_length=300, blank=True)
    image = models.ImageField(upload_to='hero/')
    link_url = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(default=0)

class JourneyStep(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    icon = models.CharField(max_length=100)
    order = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)

# Company Information
class Organization(models.Model):
    name = models.CharField(max_length=200)
    description = models.TextField()
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField()
    logo = models.ImageField(upload_to='organization/', blank=True)
    website = models.URLField(blank=True)
    # Social Media
    facebook = models.URLField(blank=True)
    twitter = models.URLField(blank=True)
    instagram = models.URLField(blank=True)
    linkedin = models.URLField(blank=True)
    whatsapp = models.CharField(max_length=20, blank=True)

class AboutUs(models.Model):
    title = models.CharField(max_length=200)
    content = models.TextField()
    vision = models.TextField()
    mission = models.TextField()
    image1 = models.ImageField(upload_to='about/', blank=True)
    image2 = models.ImageField(upload_to='about/', blank=True)
    is_active = models.BooleanField(default=True)

# Property Alerts
class PropertyAlert(models.Model):
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    property_type = models.ForeignKey(PropertyType, on_delete=models.CASCADE, null=True, blank=True)
    min_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    max_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    location = models.CharField(max_length=200, blank=True)
    min_bedrooms = models.IntegerField(null=True, blank=True)
    max_bedrooms = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
```

### API Endpoints Structure
```python
# Authentication
POST /api/auth/login/
POST /api/auth/register/
POST /api/auth/logout/
GET /api/auth/user/

# Properties
GET /api/properties/
GET /api/properties/{id}/
POST /api/properties/
PUT /api/properties/{id}/
DELETE /api/properties/{id}/

# Customer Dashboard
GET /api/customer/saved-properties/
POST /api/customer/saved-properties/
GET /api/customer/inquiries/
POST /api/customer/inquiries/
GET /api/customer/visits/
POST /api/customer/visits/

# Admin Dashboard
GET /api/admin/analytics/
GET /api/admin/users/
POST /api/admin/users/
PUT /api/admin/users/{id}/
DELETE /api/admin/users/{id}/
```

## Deployment

### Production Deployment
```bash
# Build for production
npm run build

# Deploy to your preferred hosting service
# (Vercel, Netlify, etc.)
```

## Contributing

1. Follow the existing code structure
2. Use TypeScript for all new components
3. Maintain responsive design principles
4. Test on multiple screen sizes
5. Follow the established design system

## Support

For questions or issues:
- Review the project documentation
- Check the Django REST API documentation
- Refer to React and TypeScript documentation for frontend development